local Game = {}

Game.IsUseSDK = false
Game.IsUseADSDK = true -- 广告sdk开关
Game.UrlSelector = 0
Game.isLogin = false
Game.isInited = false
Game.PauseState = 0
Game.Version = 100000
Game.JsonMap = nil
Game.vers = 0
Game.IsUseNet = false
Game.IsUseTCPNet = true

Game.IsTinyGame = false --区分有无小游戏分支
Game.IsNativeTinyGame = true --区分有无小游戏分支
Game.Channel = "Google" --区分渠道埋点
Game.Channel_IOS = false --区分IOS
Game.Channel_IOS_Notch = false --区分IOS刘海
Game.IsVscGame = false --区分是否是竖屏
Game.IsRuGame = false --区分是否是俄罗斯

Game.IsLowMemoryAlert = false --是否低内存警告

function Game.DebugTest()
local _, LuaDebuggee = pcall(require, 'LuaDebuggee')
if LuaDebuggee and LuaDebuggee.StartDebug then
	if LuaDebuggee.StartDebug('127.0.0.1', 9826) then
		print('LuaPerfect: Successfully connected to debugger!')
	else
		print('LuaPerfect: Failed to connect debugger!')
	end
else
	print('LuaPerfect: Check documents at: https://luaperfect.net')
end
end

function Game.Initialize(...)
	if(CS.StartGame.Instance.IsLuaDebug) then
		Game.DebugTest()
	end

	Game.IsUseNet = CS.StartGame.Instance.IsUseNet or true
 
    Game.IsUseSDK = CS.StartGame.Instance.IsSDK
    Game.UrlSelector = CS.StartGame.Instance.UrlSelector or 1
	
    for k, v in pairs(SortingLayerInGame) do
        SortingLayerInGame[k] = CS.UnityEngine.SortingLayer.NameToID(k)
    end

	if Game.IsTinyGame or Game.IsNativeTinyGame then
		require("TinyGame.Common_t.TinyGameGlobal")
		TinyGameConfigDefine = require "TinyGame.Define.TinyGameConfigDefine"
	end
	
    CheatCodeModule:InitCheatCode()
    ResMgr:Initialize()
    StorageMgr:Initialize()
    UIDMgr:Initialize()
    NetDataDefine:Initialize()
    TouchMonoMgr:Initialize()
    HttpClient:Initialize()
    HttpServer:Initialize()
    ConfigMgr:Initialize()

   -- coroutine.yield(5)
        
    LangMgr:Initialize()
    UIMgr:Initialize()
    TimeZoneMgr:Initialize()
    TimeMgr:Initialize()
    --SensitiveWordsMgr:Initialize()
    AudioMgr:Initialize()
	Game.InitializeLast()
    TinyGameMgr:Initialize()
	--TinyGameMgr:SetAssignEnterWay(CS.StartGame.Instance.iTinyGameEnterWay)
    HeroManager:Initialize()
	BagManager:Initialize()
	LotteryManager:Initialize()
    NetPushManager:InitEvent()
	MailManager:Initialize()
    TradeWagonsManager:Initialize()
    JJcManager:Initialize()
    DungeonManager:Initialize()
    WorldBossManager:Initialize()
	BattlePassManager:Initialize()
    TopFightManager:Initialize()


   -- coroutine.yield(1)


    Game.isInited = true
    Game.PauseState = PlayerPrefs:GetNumber("pause_state")
    Game.Login()
	
	Game.TestGM()

	CS.UnityEngine.Application.lowMemory("+",function()
		Game.IsLowMemoryAlert = true
	end)

    MainCoHelper = nil
end

function Game.InitializeLast()
	SdkHelper:Initialize()
	CurvesMgr:Initialize()
	ConfigMgr:GenerateData()
	ADMovieModule:Init()
end
-- 上线去掉调用统一接口
function Game.TestGM()
	
	--TODO HXQ 暂时打开GM命令。
	CheatCodeModule:OpenCheat(0)
end

function Game.Login()
    SceneMgr:SwitchScene(UISceneDefine.LoginScene)
end

function Game.OnApplicationPause(isPause)
    if not Game.isInited then
        return
    end

    if not isPause then
        EventMgr:Dispatch(EventID.DRAG_LOSE_PAUSE)
 		EnergyModule:EnergyRecoveryTime()
    end
    SceneMgr:OnApplicationPause(isPause)

end

function Game.Update(deltaTime, unscaledDeltaTime)
    if not Game.isInited then
        return
    end
    Time:SetDeltaTime(deltaTime, unscaledDeltaTime)
    TimeMgr:Tick(unscaledDeltaTime)
    -- GameMgr:Update(deltaTime)
    UIMgr:TickAllUI(deltaTime)
    AudioMgr:UpdateAudio(deltaTime)
    SceneMgr:TickScene(deltaTime)
  	ServerPushManager:TickSend(deltaTime)
end

function Game.StopSave(state)
    Game.PauseState = state
    Log.Info("****** StopSave:", state)
end

function Game.tg_Save()
    local timeNow = TimeMgr:GetServerTime()
	local saveId = NetUpdatePlayerData.playerInfo.id
	
	--local netData = NetDataDefine.SaveAllNetData()
    local tgData = NetTinyGameData:WriteNetData()
	
	local map = {}
	--map[saveId .. "_net"] = netData
	map[saveId .. "_tg"] = tgData
	
	local jsonMap = {}
	for k, v in pairs(map) do
		v["save_time"] = timeNow
		local t1 = Json.encode(v)
		jsonMap[k] = t1
	end
	
    
	StorageMgr:SetStorage(jsonMap, timeNow, 1)
end

--isAsync
--0: local-local
--1: Async to local & net
--2: Async local
--3: GetSave Json out to function
--4: Developer stop
function Game.Save(isAsync, funSpecial)

	if MapControllerVisit:IsVisit() then
		if funSpecial then
			funSpecial()
		end
		return
	end
	
    if Game.PauseState ~= 0 then
        if Game.PauseState == 1 then
            if isAsync == 1 then
                if funSpecial then
                    funSpecial()
                end
                return
            end
        else
            if funSpecial then
                funSpecial()
            end
            return
        end
    end
	if SocketMgr:IsKickedSave() then
		Log.Error("被顶号了，被封号了，还在保存")
		return
	end
	
    if not MapController.m_IsReadyToPlay then
        Log.Warning("###### save in not play!")
        return
    end
	
	if not SceneMgr:IsReady() then
		Log.Error("###### 场景未加载完 不要保存 save in not play!")
		return
	end
	
	if not MapController:FixCheckCanSave() then
		Log.Error("###### 存储地上格子没了？！ save in not FixCheckCanSave!")
		return
	end
	
    Log.Info("****** Saving")

    local timeNow = TimeMgr:GetServerTime()
    local saveId = NetUpdatePlayerData.playerInfo.id

    local map = Game.GetSaveDataAllInOne(saveId,isAsync == 1)
    
    Game.SaveData(map,timeNow,isAsync,funSpecial)
end

function Game.SaveData(map, timeNow, isAsync,funSpecial)
    local jsonMap = {}
    for k, v in pairs(map) do
        v["save_time"] = timeNow
        local t1 = Json.encode(v)
        jsonMap[k] = t1
    end
    StorageMgr:SetStorageAllInOne(jsonMap,map, timeNow, isAsync,funSpecial)

    --#region trace
	local thinkTable = {}
    thinkTable = {
        ["b_async"] = isAsync,
		["b_cb"] = (funSpecial and {1} or {0})[1],
    }
    SdkHelper:ThinkingTrackEvent("stroageBean_savedata", thinkTable)
	--#endregion trace
end

function Game.Load()

    local beanLocal = StorageMgr:GetBeanLocal()

    NetDataDefine.LoadAllNetData(beanLocal.saveData_net)
    WorkerController:Load(beanLocal.saveData_work)
    LimitActivityController:Load(beanLocal.saveData_activity)
    NetTinyGameData:ReadNetData(beanLocal.saveData_tg)
 
end

function Game.GetSaveDataAllInOne(saveId,all)

    local storageBeanLocal = StorageMgr:GetBeanLocal()
    return storageBeanLocal:GetSaveData(saveId,all)
end

function Game.LoadKeyTimestamp(key)
    local timeNetBackup = StorageMgr:GetStringF(key)
    if string.empty(timeNetBackup) then
        timeNetBackup = "0"
    end
    local timeNetLocal = StorageMgr:GetStringF(key .. "local")
    if string.empty(timeNetLocal) then
        timeNetLocal = "0"
    end
    return timeNetBackup, timeNetLocal
end

function Game.LoadDataAuto(key, fromNet, isIgnoreLocal)
    local timeNetBackup, timeNetLocal = Game.LoadKeyTimestamp(key)
    local timeLocalBigger = math.max(timeNetBackup, timeNetLocal)
    local ret = 0
    local data = nil
    if fromNet then
        local strJson = fromNet[key]
        if strJson then
            local fromData = Json.decode(strJson)
            if fromData then
                if isIgnoreLocal then
                    data = fromData
                    ret = 1
                else
                    local timeNetServer = fromData["save_time"]
                    if timeNetServer then
                        if tonumber(timeNetServer) >= tonumber(timeLocalBigger) then
                            data = fromData
                            ret = 1
                        end
                    end
                end
            end
        end
    end
    if data == nil then
        local keyLocalStr = key
        if timeNetLocal > timeNetBackup then
            keyLocalStr = key .. "local"
        end
        local dataLocal = StorageMgr:LoadGameData(keyLocalStr)
        if dataLocal then
            data = Json.decode(dataLocal)
            Log.Info("______load save data : ", keyLocalStr)
        end
    end
    return ret, data
end

function Game.GetAppVer() --1.0.0    1.0.1  --1.0.2
	return  CS.StartGame.Instance.StrVerApp
end

function Game.GetResVer()
	return CS.StartGame.Instance.StrVerRes;
end

--是否大于等于1.0.2    2.2.3    2.3.1
function Game:CheckGameVersion101()
	local version = Game.GetAppVer() 
	local version1,version2,version3 = string.match(version, "(%d+).(%d+).(%d+)")
	local versionArray = {v2n(version1), v2n(version2), v2n(version3)}
	local isTgVersion = false
	if versionArray[1] > 1
		or (versionArray[1] == 1 and versionArray[2] > 0)
		or (versionArray[1] == 1 and versionArray[2] == 0 and versionArray[3] >= 1)
		then
		isTgVersion = true
	end

	return isTgVersion --isTgVersion
end
 
--是否大于等于1.0.2    2.2.3    2.3.1
function Game:CheckGameVersion107()
	local version = Game.GetAppVer()
	local version1,version2,version3 = string.match(version, "(%d+).(%d+).(%d+)")
	local versionArray = {v2n(version1), v2n(version2), v2n(version3)}
	local isTgVersion = false
	if versionArray[1] > 1
		or (versionArray[1] == 1 and versionArray[2] > 0)
		or (versionArray[1] == 1 and versionArray[2] == 0 and versionArray[3] >= 7)
		then
		isTgVersion = true
	end

	return isTgVersion --isTgVersion
end

function Game.CompareNeedAppVer(strVer,tgVer,iosVer,vscVer)
	if Game.IsVscGame and not vscVer then
		return true
	end
	
	local strVersion = Game.GetAppVer()

	local needVer = strVer
	if Game.IsTinyGame and tgVer then
		needVer = tgVer
	elseif Game.IsTinyGame == false and Game.Channel_IOS and iosVer then
		needVer = iosVer
	end
	
	-- 特殊版本号
	if strVersion == "0.0.0" then
		return true
	end

	--local strVersion = "1.0.5"
	local version1,version2,version3 = string.match(strVersion, "(%d+).(%d+).(%d+)")
	local versionArray = {v2n(version1), v2n(version2), v2n(version3)}


	local Ver1, Ver2, Ver3 = string.match(needVer, "(%d+).(%d+).(%d+)")
	local VerArray = {v2n(Ver1), v2n(Ver2), v2n(Ver3)}


	if versionArray[1] > VerArray[1]
		or (versionArray[1] == VerArray[1] and versionArray[2] > VerArray[2])
		or (versionArray[1] == VerArray[1] and versionArray[2] == VerArray[2] and versionArray[3] >= VerArray[3])
		then
		return true
	else
		return false
	end

end

function Game.GetServerSwitch()
    if Game.ServerSwitch == nil then
        if CS.StartGame.Instance.GetServerSwitch then
            Game.ServerSwitch = Json.decode(CS.StartGame.Instance:GetServerSwitch()) or {}
            Log.Info("__GetServerSwitch: "..CS.StartGame.Instance:GetServerSwitch())
        else
            Game.ServerSwitch = {}
            Log.Info("__GetServerSwitch: noooo")
        end
    end
    return Game.ServerSwitch
end


function Game.GetABGroup()
	--do
		--return "2"
	--end
	return _G.CS.StartGame.Instance:GetGroup()
end

function Game.IsWechatGame()
	return _G.CS.StartGame.Instance:IsWechatGame()
end

return Game