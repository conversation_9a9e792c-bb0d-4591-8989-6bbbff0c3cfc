-- 贸易货车活动管理器

local TradeWagonsManager = Class()
local M = TradeWagonsManager
local tradeTruckHandler = require("Proto.Handler.NetRequest.TradeTruck")
local tradeTrainHandler = require("Proto.Handler.NetRequest.TradeTrain")
local fightHandler = require("Proto.Handler.NetRequest.Fight")

local WagonIcon = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_truck1.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_truck2.png",
    [3] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_truck3.png",
    [4] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_truck4.png",
    [5] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_truck5.png",
}

local WagonDetailIcon = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_N2.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_R2.png",
    [3] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_SR2.png",
    [4] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_SSR2.png",
    [5] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_UR2.png",
}

local WagonSpine = {
    [1] = "Spine/trucks_1/huoche_bai_SkeletonData.asset",
    [2] = "Spine/trucks_2/huoche_lv_SkeletonData.asset",
    [3] = "Spine/trucks_3/huoche_lan_SkeletonData.asset",
    [4] = "Spine/trucks_4/huoche_zi_SkeletonData.asset",
    [5] = "Spine/trucks_5/huoche_huang_SkeletonData.asset",
}

local WagonMaterial = {
    [1] = "Spine/trucks_1/huoche_bai_Material.mat",
    [2] = "Spine/trucks_2/huoche_lv_Material.mat",
    [3] = "Spine/trucks_3/huoche_lan_Material.mat",
    [4] = "Spine/trucks_4/huoche_zi_Material.mat",
    [5] = "Spine/trucks_5/huoche_huang_Material.mat",
}

local QualityIcon = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_N.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_R.png",
    [3] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_SR.png",
    [4] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_SSR.png",
    [5] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_UR.png",
}

local QualityBorderIcon = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_N3.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_R3.png",
    [3] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_SR3.png",
    [4] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_SSR3.png",
    [5] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_UR3.png",
}

local QualityBgIcon = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_N4.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_R4.png",
    [3] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_SR4.png",
    [4] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_SSR4.png",
    [5] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_UR4.png",
}

local QualityBgDetailIcon = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_N5.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_R5.png",
    [3] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_SR5.png",
    [4] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_SSR5.png",
    [5] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_pingzhi_UR5.png",
}

local RewardQualityIcon = {
    [1] = "Sprite/ui_slg_beibao/beibao_daojukuang_green.png",
    [2] = "Sprite/ui_slg_beibao/beibao_daojukuang_blue.png",
    [3] = "Sprite/ui_slg_beibao/beibao_daojukuang_purple.png",
    [4] = "Sprite/ui_slg_beibao/beibao_daojukuang_gold.png",
}

local TrainCarriageIcon = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_train1_2_1.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_train1_2_2.png",
    [3] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_train1_2_3.png",
    [4] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_train1_2_4.png",
    [5] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_train1_2_5.png",
}

local RewardBgIcon = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_jlk1.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_jlk2.png",
    [3] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_jlk3.png",
    [4] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_jlk4.png",
    [5] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_jlk5.png",
}

local RewardArrowIcon = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_jlk1_2.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_jlk2_2.png",
    [3] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_jlk3_2.png",
    [4] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_jlk4_2.png",
    [5] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_jlk5_2.png",
}

local TeamPosBorderIcon = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win5_danweidi2.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win5_danweidi2_2.png",
    [3] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win5_danweidi2_3.png",
    [4] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win5_danweidi2_4.png",
    [5] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win5_danweidi2_5.png",
}

local WagonsSelectEffectPos = {
    [1] = { -3, -28 },
    [2] = { -13, -28 },
    [3] = { -18, -28 },
    [4] = { -18, -28 },
    [5] = { -24, -28 },
}

function M:ctor()
    self.isInit = false

    -- 全部信息
    self.loadInfo = {
        today_rob_times = 0,        -- 今日已掠夺次数
        today_rob_times_buy = 0,    -- 今日已购买掠夺次数
        today_trade_times = 0,      -- 今日已运输次数
        other_trade_info = {},      -- 可抢夺的他人货车列表
        trade_index_info = {},      -- 个人货车位置信息
        last_reset_time = 0,        -- 最后一次重置可抢夺的他人货车列表时间戳
    }

    -- 构造个人货车位置信息（后端可能只下发两个车位的数据  小心报错）
    for i = 1, 4, 1 do
        local info = {
            index = i,               -- 位置索引
            status = 0,              -- 发车状态
            quality = 1,             -- 品质
            begin_timestamp = 0,     -- 发车开始时间
            be_attack_times = 0,     -- 被攻击次数
            record_id = 0,           -- 发车记录
            is_unlock = false,       -- 是否解锁
        }
        table.insert(self.loadInfo.trade_index_info, info)
    end

    self.wagonsInfoMe = {}           -- 我的货车信息
    self.wagonsInfoOther = {}        -- 他人的货车信息
    self.wagonsRefreshMe = {}        -- 我的货车刷新次数

    ---------------------------------------- 华丽的分隔线 ----------------------------------------

    -- 火车加载数据
    self.trainLoadInfo = {
        train_gift_buy_times = 0,    -- 今日购买火车礼包次数
        league_depart_times = 0,     -- 今日联盟发车次数
        league_train = {},           -- 我的联盟火车
        can_loot_train = {},         -- 可掠夺的火车
        train_call_times = 0,        -- 今日玩家火车召唤次数
    }
end

function M:Initialize()
    RedPointMgr:AddNodeTo(RedID.Root, RedID.TradeWagons, function()
        local redDic = self:CheckRedPoint()
        return redDic and 1 or 0
    end)
end

function M:Tick(deltaTime)
    self:CheckTrainEnd()
end

--- 界面上调用
function M:TickUI()
    self:CheckTrainDepart()
end

--- 检查火车发车
function M:CheckTrainDepart()
    if self.trainLoadInfo.league_train then
        for _, train in ipairs(self.trainLoadInfo.league_train) do
            if train.status == TRADE_TRAIN_STATUS.Ready and train.depart_timestamp then
                local remain = train.depart_timestamp - TimeMgr:GetServerTime()
                -- 已经到了发车时间
                if remain <= 0 then
                    train.status = TRADE_TRAIN_STATUS.Transporting
                    self:RequestTrainInfo(train.record_id, function (data)
                        UI_UPDATE(UIDefine.UI_TradeWagonsWindow, 3)
                        UI_UPDATE(UIDefine.UI_TradeWagonsCarriage, 1)
                    end, nil)
                end
            end
        end
    end
end

--- 检查火车到站
function M:CheckTrainEnd()
    if self.trainLoadInfo.league_train then
        for _, train in ipairs(self.trainLoadInfo.league_train) do
            if (train.status == TRADE_TRAIN_STATUS.Transporting
            or train.status == TRADE_TRAIN_STATUS.FinishedNotGiveReward)
            and train.end_timestamp then
                local remain = train.end_timestamp - TimeMgr:GetServerTime()
                -- 火车已经到站
                if remain <= 0 then
                    train.status = TRADE_TRAIN_STATUS.FinishedHadGiveReward
                    self:RequestTrainInfo(train.record_id, function (data)

                    end, TRADE_TRAIN_STATUS.FinishedHadGiveReward)
                end
            end
        end
    end
end

--region ----------------------------------------- 读表 -----------------------------------------

--- 获取货车全局配置
--- @param id number 全局配置 ID
--- @return table config 配置数据
function M:GetTradeSettingConfig(id)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_trade_setting, id)
    if config then
        return config.value
    else
        Log.Error("获取 贸易货车设置 配置表错误")
    end
end

--- 根据 ID 获取货车配置数据
--- @param id number ID
--- @return table config 配置数据
function M:GetTradeConfigByID(id)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_trade, id)
    if config then
        return config
    else
        Log.Error("获取 贸易货车设置 配置表错误")
    end
end

--- 根据类型和品质获取货车配置数据
--- @param type number 类型
--- @param quality number 品质
--- @return table config 配置数据
function M:GetTradeConfigByTypeAndQuality(type, quality)
    local config = ConfigMgr:GetData(ConfigDefine.ID.slg_trade)
    if config then
        for _, value in pairs(config) do
            if value.trade_type == type and value.quality == quality then
                return value
            end
        end
    else
        Log.Error("获取 贸易货车 配置表错误")
    end
end

--- 根据类型获取货车概率配置
--- @param type number 类型
--- @return table config 配置数据
function M:GetTradeRateConfigByType(type)
    local result = {}
    local config = ConfigMgr:GetData(ConfigDefine.ID.slg_trade_rate)
    if config then
        for _, value in pairs(config) do
            if value.trade_type == type then
                table.insert(result, value)
            end
        end
    else
        Log.Error("获取 贸易货车刷新 配置表错误")
    end
    -- 按照刷新次数排序
    table.sort(result, function (a, b)
        if a.time ~= b.time then
            return a.time < b.time
        end
        return false
    end)
    return result
end

--endregion -------------------------------------- 读表 -----------------------------------------

--region ----------------------------------------- 货车协议 -----------------------------------------

--- 请求所有信息
--- @param callback function 回调
function M:RequestAllInfo(callback)
    tradeTruckHandler:OnReqTradeTruckAllInfoReq(function (isSuccess, data)
        if isSuccess then
            self.isInit = true

            -- 刷新次数
            self.loadInfo.today_rob_times = data.today_rob_times
            self.loadInfo.today_rob_times_buy = data.today_rob_times_buy
            self.loadInfo.today_trade_times = data.today_trade_times
            self.loadInfo.other_trade_info = data.other_trade_info
            self.loadInfo.last_reset_time = data.last_reset_time

            -- 找到车位再赋值
            if data.trade_index_info then
                for _, value in pairs(data.trade_index_info) do
                    if value.index == self.loadInfo.trade_index_info[value.index].index then
                        self.loadInfo.trade_index_info[value.index] = value
                    end
                end
            end

            -- 根据车位状态，计算货车结束时间戳
            if self.loadInfo.trade_index_info then
                for _, value in pairs(self.loadInfo.trade_index_info) do
                    if value.status == TRADE_WAGONS_STATUS.Depart then
                        local config = self:GetTradeConfigByTypeAndQuality(1, value.quality)
                        if config then
                            local duration = config.time
                            value.end_timestamp = value.begin_timestamp + duration
                        end
                    end
                end
            end

            -- 根据车位状态，计算货车结束时间戳
            if self.loadInfo.other_trade_info then
                for _, value in pairs(self.loadInfo.other_trade_info) do
                    local config = self:GetTradeConfigByTypeAndQuality(1, value.quality)
                    if config then
                        local duration = config.time
                        value.end_timestamp = value.begin_time + duration
                    end
                end
            end
            if callback then callback(data) end
        end
    end)
end

--- 请求我的货车信息
--- @param index number 位置
--- @param callback function 回调
function M:RequestWagonInfoMe(index, callback)
    tradeTruckHandler:OnReqTradeTruckInfoReq(index, function (isSuccess, data)
        if isSuccess then
            -- 刷新我的货车信息
            self.wagonsInfoMe[index] = data
            self.wagonsRefreshMe[index] = data.info.refresh_times
            if callback then callback(data) end
        end
    end)
end

--- 请求他人的货车信息
--- @param record_id number 记录 ID
--- @param callback function 回调
function M:RequestWagonInfoOther(record_id, callback)
    tradeTruckHandler:OnReqTradeTruckTargetTruckInfoReq(record_id, function (isSuccess, data)
        if isSuccess then
            self.wagonsInfoOther[record_id] = data

            for _, value in pairs(self.loadInfo.other_trade_info) do
                if value.record_id == record_id then
                    value.attacked_times = data.truck_info.attacked_times

                    local config = self:GetTradeConfigByTypeAndQuality(1, value.quality)
                    if config then
                        local duration = config.time
                        value.end_timestamp = value.begin_time + duration
                    end
                end
            end
            if callback then callback(data) end
        end
    end)
end

--- 请求重置他人货车
--- @param callback function 回调
function M:RequestWagonReset(callback)
    tradeTruckHandler:OnReqTradeTruckResetReq(function (isSuccess, data)
        if isSuccess then
            -- 更新他人货车
            self.loadInfo.other_trade_info = data.other_trade_info

            -- 更新可掠夺的火车
            self.trainLoadInfo.can_loot_train = data.train_record
            if self.trainLoadInfo.can_loot_train then
                self:ComputeTrainEndTimestamp(self.trainLoadInfo.can_loot_train)
            end

            -- 根据车位状态，计算货车结束时间戳
            if self.loadInfo.other_trade_info then
                for _, value in pairs(self.loadInfo.other_trade_info) do
                    local config = self:GetTradeConfigByTypeAndQuality(1, value.quality)
                    if config then
                        local duration = config.time
                        value.end_timestamp = value.begin_time + duration
                    end
                end
            end
            if callback then callback(data) end
        end
    end)
end

--- 请求掠夺货车
--- @param record_id number 记录 ID
--- @param callback function 回调
function M:RequestWagonPlunder(record_id, callback)
    tradeTruckHandler:OnReqTradeTruckLootReq(record_id, function (isSuccess, data)
        if isSuccess then
            self.loadInfo.today_rob_times = data.today_looted_times
            if callback then callback(data) end

            local rewards = {}
            for _, value in ipairs(data.rewards) do
                table.insert(rewards, {
                    id = value.code,
                    num = value.amount
                })
            end
            UI_UPDATE(UIDefine.UI_TradeWagonsWindow, 1)
            UI_UPDATE(UIDefine.UI_TradeWagonsView, 1)
            UI_UPDATE(UIDefine.UI_ActivityRankCenter, 2)
            RedPointMgr:Dirty(RedID.TradeWagons)
            self:TrackEventPlunder(data.truck.player.id, data.is_success, rewards)
        end
    end)
end

--- 请求刷新货车品质
--- @param index number 车位
--- @param callback function 回调
function M:RequestRefreshWagonQuality(index, callback)
    tradeTruckHandler:OnReqTradeTruckQuaRefresh(index, function (isSuccess, data)
        if isSuccess then
            -- 刷新我的货车信息
            self.wagonsInfoMe[index].info.rewards = data.rewards
            self.wagonsInfoMe[index].info.quality = data.quality
            self.wagonsRefreshMe[index] = data.refresh_times
            if callback then callback(data) end

            self:TrackEventRefreshWagon(data.quality)
        end
    end)
end

--- 请求发车
--- @param index number 车位
--- @param callback function 回调
function M:RequestWagonDepart(index, callback)
    local function SendTruckDepart()
        tradeTruckHandler:OnReqTradeTruckDepart(index, function (isSuccess, data)
            if isSuccess then
                -- 刷新我的货车信息
                self.wagonsInfoMe[index].info = data.truck
                self.loadInfo.trade_index_info[index] = data.index_info

                -- 刷新次数
                self.loadInfo.today_trade_times = data.today_trade_times

                -- 根据车位状态，计算货车结束时间戳
                for _, value in pairs(self.loadInfo.trade_index_info) do
                    if value.status == TRADE_WAGONS_STATUS.Depart then
                        local config = self:GetTradeConfigByTypeAndQuality(1, value.quality)
                        if config then
                            local duration = config.time
                            value.end_timestamp = value.begin_timestamp + duration
                        end
                    end
                end

                if callback then callback(data) end

                local rewards = {}
                for _, value in ipairs(data.truck.rewards) do
                    table.insert(rewards, {
                        id = value.reward.code,
                        num = value.reward.amount
                    })
                end
                self:TrackEventDepartWagon(data.truck.record_id, rewards)
            end
        end)
    end
    local teamType = self:GetBattleTypeByIndex(index)
    if teamType then
        HeroManager:CheckTradeCarBattleListSave(teamType, SendTruckDepart)
    end
end

--- 请求领取货车奖励
--- @param index number 车位
--- @param callback function 回调
function M:RequestWagonReward(index, callback)
    tradeTruckHandler:OnReqTradeTruckFetchReward(index, function (isSuccess, data)
        if isSuccess then
            -- 刷新我的货车信息
            if self.wagonsInfoMe[index] then
                self.wagonsInfoMe[index].info.status = TRADE_WAGONS_STATUS.Ready
            end
            if self.loadInfo.trade_index_info[index] then
                self.loadInfo.trade_index_info[index].status = TRADE_WAGONS_STATUS.Ready
            end
            if callback then callback(data) end
        end
    end)
end

--- 请求运输记录
--- @param callback function 回调
function M:RequestDepartRecord(callback)
    tradeTruckHandler:OnReqTradeTruckTransportRecordReq(function (isSuccess, data)
        if isSuccess then
            if callback then callback(data) end
        end
    end)
end

--- 请求掠夺记录
--- @param callback function 回调
function M:RequestPlunderRecord(callback)
    tradeTruckHandler:OnReqTradeTruckLootRecordReq(function (isSuccess, data)
        if isSuccess then
            if callback then callback(data) end
        end
    end)
end

--- 请求通缉
--- @param callback function 回调
function M:RequestWanted(battle_id, callback)
    tradeTruckHandler:OnReqTradeTruckWantedPredatorsReq(battle_id, function (isSuccess, data)
        if isSuccess then
            if callback then callback(data) end
        end
    end)
end

--endregion -------------------------------------- 货车协议 -----------------------------------------

--region ----------------------------------------- 火车协议 -----------------------------------------

--- 请求火车加载数据
--- @param callback function 回调
function M:RequestTrainLoadData(callback)
    tradeTrainHandler:OnReqTradeTrainOpenLoadDataReq(function (isSuccess, data)
        if isSuccess then
            self.trainLoadInfo = data
            if self.trainLoadInfo.league_train then
                for _, value in ipairs(self.trainLoadInfo.league_train) do
                    if value.status == TRADE_TRAIN_STATUS.Transporting then
                        self:ComputeTrainEndTimestamp(value)
                    end
                end
            end

            if self.trainLoadInfo.can_loot_train then
                self:ComputeTrainEndTimestamp(self.trainLoadInfo.can_loot_train)
            end
            if callback then callback(data) end
        end
    end)
end

--- 请求购买火车礼包
--- @param pay_id number 礼包配置 ID
--- @param callback function 回调
function M:RequestTrainBuyGift(pay_id, callback)
    tradeTrainHandler:OnReqTradeTrainBuyTrainCallGiftReq(pay_id, function (isSuccess, data)
        if isSuccess then
            if callback then callback(data) end
        end
    end)
end

--- 请求召唤火车
--- @param callback function 回调
function M:RequestCallTrain(callback)
    tradeTrainHandler:OnReqTradeTrainCallTrainReq(function (isSuccess, data)
        if isSuccess then
            if self.trainLoadInfo.league_train then
                local hasTrain = false
                for index, value in ipairs(self.trainLoadInfo.league_train) do
                    if value.record_id == data.train.record_id then
                        self.trainLoadInfo.league_train[index] = data.train
                        hasTrain = true
                    end
                end
                if not hasTrain then
                    table.insert(self.trainLoadInfo.league_train, data.train)
                end

                for _, value in ipairs(self.trainLoadInfo.league_train) do
                    if value.status == TRADE_TRAIN_STATUS.Transporting then
                        self:ComputeTrainEndTimestamp(value)
                    end
                end

                local config29 = self:GetTradeSettingConfig(29)
                if config29 then
                    local costTable = string.split(config29, "|")
                    local needItemID = v2n(costTable[1])
                    local needNum = v2n(costTable[2])
                    self:TrackEventCallTrainCost(needNum)
                end
            end
            if callback then callback(data) end
        end
    end)
end

--- 请求刷新火车品质
--- @param callback function 回调
function M:RequestTrainQualityRefresh(record_id, callback)
    tradeTrainHandler:OnReqTradeTrainRefreshTrainQualityReq(record_id, function (isSuccess, data)
        if isSuccess then
            if self.trainLoadInfo.league_train then
                for index, value in ipairs(self.trainLoadInfo.league_train) do
                    if value.record_id == data.record.record_id then
                        self.trainLoadInfo.league_train[index] = data.record

                        if self.trainLoadInfo.league_train[index].status == TRADE_TRAIN_STATUS.Transporting then
                            self:ComputeTrainEndTimestamp(value)
                        end

                        self:TrackEventRefreshTrain(value.record_id, data.record.train_quality, data.record.rewards_list)
                    end
                end
            end
            if callback then callback(data) end
        end
    end)
end

--- 请求掠夺火车
--- @param record_id number 火车记录 ID
--- @param callback function 回调
function M:RequestPlunderTrain(record_id, callback)
    tradeTrainHandler:OnReqTradeTrainRobTrainReq(record_id, function (isSuccess, data)
        if isSuccess then
            -- 刷新今日掠夺次数
            self.loadInfo.today_rob_times = data.today_rob_train_times

            -- 刷新可掠夺火车
            self.trainLoadInfo.can_loot_train = data.record
            if self.trainLoadInfo.can_loot_train then
                self:ComputeTrainEndTimestamp(self.trainLoadInfo.can_loot_train)
            end

            local train = data.record

            local battleLogs = train.battle_logs

            -- 按照时间戳排序，最新的排前面
            table.sort(battleLogs, function (a, b)
                if a.battle_timestamp ~= b.battle_timestamp then
                    return a.battle_timestamp > b.battle_timestamp
                end
                return false
            end)

            local curBattle = battleLogs[1]
            if IsTableNotEmpty(curBattle) then

                local winCount = 0
                for i = 1, 3, 1 do
                    local battleResultInfo = curBattle.battle_result_info[i]
                    if IsTableNotEmpty(battleResultInfo) then
                        local battleResult = battleResultInfo.battle_result
                        if battleResult == 1 then
                            winCount = winCount + 1
                        end
                    end
                end

                local isWin = winCount >= 2

                self:TrackEventPlunderTrain(train.record_id, curBattle.attacker_player.id, isWin, curBattle.robed_rewards)
            end

            if callback then callback(data) end
        end
    end)
end

--- 请求火车车厢排队
--- @param record_id number 火车记录 ID
--- @param index number 车厢类型（ 0 代表火车头 1 ~ 4 代表车厢 ）
--- @param callback function 回调
function M:RequestTrainQueueUp(record_id, index, callback)
    tradeTrainHandler:OnReqTradeTrainWaitingTrainReq(record_id, index, function (isSuccess, data)
        if isSuccess then
            if self.trainLoadInfo.league_train then
                for key, value in ipairs(self.trainLoadInfo.league_train) do
                    if value.record_id == data.record_id then
                        self.trainLoadInfo.league_train[key].passegers_list = data.passegers_list

                        if value.status == TRADE_TRAIN_STATUS.Transporting then
                            self:ComputeTrainEndTimestamp(value)
                        end

                        self:TrackEventQueueUpTrain(value.record_id, value.captain, data.passegers_list)
                    end
                end
            end
            if callback then callback(data) end
        end
    end)
end

--- 请求火车掠夺记录
--- @param callback function 回调
function M:RequestPlunderTrainRecord(callback)
    tradeTrainHandler:OnReqTradeTrainRobRrcordReq(function (isSuccess, data)
        if isSuccess then
            if callback then callback(data) end
        end
    end)
end

--- 请求火车运输记录
--- @param callback function 回调
function M:RequestDepartTrainRecord(callback)
    tradeTrainHandler:OnReqTradeTraintransportRecordReq(function (isSuccess, data)
        if isSuccess then
            if callback then callback(data) end
        end
    end)
end

--- 请求火车指派列车长
--- @param record_id number 火车记录 ID
--- @param captain_id number 列车长 ID
--- @param callback function 回调
function M:RequestAssignConductor(record_id, captain_id, callback)
    tradeTrainHandler:OnReqTradeTrainChoiceCaptainReq(record_id, captain_id, function (isSuccess, data)
        if isSuccess then
            if self.trainLoadInfo.league_train then
                for index, value in ipairs(self.trainLoadInfo.league_train) do
                    if value.record_id == data.change_train.record_id then
                        self.trainLoadInfo.league_train[index] = data.change_train

                        if self.trainLoadInfo.league_train[index].status == TRADE_TRAIN_STATUS.Transporting then
                            self:ComputeTrainEndTimestamp(self.trainLoadInfo.league_train[index])
                        end
                    end
                end
            end
            if callback then callback(data) end
        end
    end)
end

--- 请求火车信息
--- @param record_id number 火车记录 ID
--- @param callback function 回调
--- @param status number|nil 已经设定的状态
function M:RequestTrainInfo(record_id, callback, status)
    tradeTrainHandler:OnReqTradeTrainTrainInfoReq(record_id, function (isSuccess, data)
        if isSuccess then
            if self.trainLoadInfo.league_train then
                for index, value in ipairs(self.trainLoadInfo.league_train) do
                    if value.record_id == data.train_info.record_id then
                        self.trainLoadInfo.league_train[index] = data.train_info

                        -- 已经设定了状态，就把原本的状态赋值给火车，防止后端没有改变状态，导致不断发起请求
                        if status then
                            self.trainLoadInfo.league_train[index].status = status
                        end

                        if self.trainLoadInfo.league_train[index].status == TRADE_TRAIN_STATUS.Transporting then
                            self:ComputeTrainEndTimestamp(self.trainLoadInfo.league_train[index])
                        end
                    end
                end
            end
            self:ComputeTrainEndTimestamp(data.train_info)
            if callback then callback(data) end
        end
    end)
end

--- 请求交换队伍顺序
--- @param teamType1 number 队伍类型 1
--- @param teamType2 number 队伍类型 2
--- @param callback function 回调
function M:RequestExchangeTeamOrder(teamType1, teamType2, callback)
    fightHandler:OnReqTeamExchange(teamType1, teamType2, function (isSuccess, data)
        if isSuccess then
            if callback then callback(data) end
        end
    end)
end

--- 乘客列表变化推送
--- @param data table 乘客列表数据
function M:OnHandlePushTrainPassagerChange(data)
    if self.trainLoadInfo.league_train then
        for key, value in ipairs(self.trainLoadInfo.league_train) do
            if value.record_id == data.record_id then
                self.trainLoadInfo.league_train[key].passegers_list = data.passegers_list

                if value.status == TRADE_TRAIN_STATUS.Transporting then
                    self:ComputeTrainEndTimestamp(value)
                end
            end
        end
    end
    UI_UPDATE(UIDefine.UI_TradeWagonsCarriage, 2)
end

--- 召唤火车推送
--- @param data table 火车数据
function M:OnHandlePushTrain(data)
    if self.trainLoadInfo.league_train then
        local hasTrain = false
        for index, value in ipairs(self.trainLoadInfo.league_train) do
            if value.record_id == data.train_info.record_id then
                self.trainLoadInfo.league_train[index] = data.train_info
                hasTrain = true
            end
        end
        if not hasTrain then
            table.insert(self.trainLoadInfo.league_train, data.train_info)
        end

        for _, value in ipairs(self.trainLoadInfo.league_train) do
            if value.status == TRADE_TRAIN_STATUS.Transporting then
                self:ComputeTrainEndTimestamp(value)
            end
        end
    end
    UI_UPDATE(UIDefine.UI_TradeWagonsWindow, 5)
end

--- 指派列车长推送
--- @param data table 火车数据
function M:OnHandlePushTrainCaptain(data)
    if self.trainLoadInfo.league_train then
        for index, value in ipairs(self.trainLoadInfo.league_train) do
            if value.record_id == data.train_info.record_id then
                self.trainLoadInfo.league_train[index] = data.train_info
                self.trainLoadInfo.league_train[index].status = TRADE_TRAIN_STATUS.Ready

                if value.status == TRADE_TRAIN_STATUS.Transporting then
                    self:ComputeTrainEndTimestamp(value)
                end
            end
        end
    end
    UI_UPDATE(UIDefine.UI_TradeWagonsWindow, 2)
end

--endregion -------------------------------------- 火车协议 -----------------------------------------

--region ----------------------------------------- 接口 -----------------------------------------

--- 判断英雄是否在货车上
--- @param heroID number 英雄 ID
--- @return boolean flag 是否在货车上
--- @return number index 货车位置
function M:IsHeroInWagon(heroID)
    for _, wagon in pairs(self.wagonsInfoMe) do
        for _, value in pairs(wagon.info.heroes) do
            if value.code == heroID then
                return true, wagon.index
            end
        end
    end
    return false, 0
end

function M:GetWagonIcon(index)
    return WagonIcon[index]
end

function M:GetWagonDetailIcon(index)
    return WagonDetailIcon[index]
end

function M:GetWagonSpine(index)
    return WagonSpine[index]
end

function M:GetWagonMaterial(index)
    return WagonMaterial[index]
end

function M:GetQualityIcon(quality)
    return QualityIcon[quality]
end

function M:GetQualityBorderIcon(quality)
    return QualityBorderIcon[quality]
end

function M:GetQualityBgIcon(quality)
    return QualityBgIcon[quality]
end

function M:GetQualityBgDetailIcon(quality)
    return QualityBgDetailIcon[quality]
end

function M:GetRewardQualityIcon(quality)
    return RewardQualityIcon[quality]
end

function M:GetTrainCarriageIcon(quality)
    return TrainCarriageIcon[quality]
end

function M:GetRewardBgIcon(quality)
    return RewardBgIcon[quality]
end

function M:GetRewardArrowIcon(quality)
    return RewardArrowIcon[quality]
end

function M:GetTeamPosBorderIcon(quality)
    return TeamPosBorderIcon[quality]
end

function M:GetWagonsSelectEffectPos(quality)
    return WagonsSelectEffectPos[quality]
end

--- 根据车位获取队伍编号
--- @param index number 车位
--- @return integer|nil teamType 队伍编号
function M:GetBattleTypeByIndex(index)
    local teamType
    if index == 1 then
        teamType = BATTLE_TEAM_TYPE.TRADE_CAR_1
    elseif index == 2 then
        teamType = BATTLE_TEAM_TYPE.TRADE_CAR_2
    elseif index == 3 then
        teamType = BATTLE_TEAM_TYPE.TRADE_CAR_3
    elseif index == 4 then
        teamType = BATTLE_TEAM_TYPE.TRADE_CAR_4
    end
    return teamType
end

--- 获取红点数量
--- @return number|nil totalCount 红点总数量
--- @return boolean hasRedPoint 是否有红点
function M:GetRedPointCount()
    local departCount = self.loadInfo.today_trade_times
    local departCountMax = v2n(self:GetTradeSettingConfig(11))
    local departRemainCount = departCountMax - departCount
    local emptyCount = 0
    local rewardCount = 0
    local unlockPos = self:GetUnlockPos()
    for _, value in pairs(self.loadInfo.trade_index_info) do
        if value.status == TRADE_WAGONS_STATUS.Finish then
            rewardCount = rewardCount + 1
        elseif value.status == TRADE_WAGONS_STATUS.Depart then
            if value.end_timestamp then
                local remain = value.end_timestamp - TimeMgr:GetServerTime()
                if remain > 0 then

                else
                    rewardCount = rewardCount + 1
                end
            end
        elseif value.status == TRADE_WAGONS_STATUS.None
        or value.status == TRADE_WAGONS_STATUS.Ready then
            local isUnlock = unlockPos[tostring(value.index)]
            if isUnlock then
                emptyCount = emptyCount + 1
            end
        end
    end

    local availableCount
    if emptyCount < departRemainCount then
        availableCount = emptyCount
    else
        availableCount = departRemainCount
    end

    if not self.isInit then
        availableCount = 0
    end

    local totalCount = math.min(availableCount + rewardCount, 4)
    return totalCount, totalCount > 0
end

--- 获取掠夺次数
--- @return integer plunderCount 掠夺次数
function M:GetPlunderCount()
    -- 货车和火车共用掠夺次数
    return self.loadInfo.today_rob_times
end

--- 检查是否有掠夺红点
--- @return boolean hasPlunderRedPoint 是否有掠夺红点
function M:CheckPlunderRedPoint()
    local plunderCount = self:GetPlunderCount()
    local plunderCountMax = v2n(self:GetTradeSettingConfig(6))
    local plunderCountRemain = plunderCountMax - plunderCount
    if not self.isInit then
        plunderCountRemain = 0
    end
    local hasPlunderRedPoint = plunderCountRemain > 0
    return hasPlunderRedPoint
end

--- 检查是否有红点
--- @return boolean hasRedPoint 是否有红点
function M:CheckRedPoint()
    local _, hasRedPoint = self:GetRedPointCount()
    local hasPlunderRedPoint = self:CheckPlunderRedPoint()
    return hasRedPoint or hasPlunderRedPoint
end

--- 获取解锁车位
--- @return table unlockPos 解锁车位
function M:GetUnlockPos()
    local unlockPos = {}

    -- 默认位置 1 和 2
    local defaultPos = self:GetTradeSettingConfig(3)
    local defaultPosTable = string.split(defaultPos, "|")
    for _, value in ipairs(defaultPosTable) do
        unlockPos[value] = true
    end

    -- 等级达到条件，解锁 3
    local pos3 = self:GetTradeSettingConfig(4)
    local pos3Table = string.split(pos3, "|")
    local level = NetUpdatePlayerData:GetLevel()
    if level >= v2n(pos3Table[2]) then
        unlockPos[pos3Table[1]] = true
    end

    -- 购买月卡，解锁 4
    local pos4 = self:GetTradeSettingConfig(5)
    local pos4Table = string.split(pos4, "|")
    local isUnlockMonthCard = NetMonthCardData:CheckUnlock(v2n(pos4Table[2]))
    if isUnlockMonthCard then
        unlockPos[pos4Table[1]] = true
    end

    return unlockPos
end

function M:GetIsTradeCarType(teamType)
    return teamType == BATTLE_TEAM_TYPE.TRADE_CAR_1 or teamType == BATTLE_TEAM_TYPE.TRADE_CAR_2
            or teamType == BATTLE_TEAM_TYPE.TRADE_CAR_3 or teamType == BATTLE_TEAM_TYPE.TRADE_CAR_4
end

function M:GetIsTradeTrainType(teamType)
    return teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_1 or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_2
            or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_3 or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_1_1 
            or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_1_2 or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_1_3 
            or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_2_1 or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_2_2 
            or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_2_3 or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_3_1 
            or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_3_2 or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_3_3
end

function M:GetIsTradeTrainDefendType(teamType)
    return  teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_1_1 or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_1_2 
            or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_1_3 or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_2_1
            or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_2_2 or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_2_3
            or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_3_1 or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_3_2
            or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_3_3
end

function M:GetIsTradeTrainRobType(teamType)
    return teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_1 or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_2
            or teamType == BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_3 
end

function M:GetTradeTrainRobTypeByIndex(index)
    local teamType 
    if index == 1 then
        teamType = BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_1
    elseif index == 2 then
        teamType = BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_2
    elseif index == 3 then
        teamType = BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_3
    end
    return teamType
end

--- 获取火车掠夺队伍
--- @param index number 队伍索引
--- @return table team 队伍信息
function M:GetTrainPlunderTeam(index)
    local teamType = self:GetTradeTrainRobTypeByIndex(index)
    local team = HeroManager:GetBattleTeamByType(teamType)
    return team
end

--- 计算火车到站时间
--- @param train table 火车
function M:ComputeTrainEndTimestamp(train)
    if not train.train_quality then return end
    local totalTime = 0
    for _, value in ipairs(train.train_quality) do
        if value.part == TRADE_TRAIN_CARRIAGE.Head then
            local configID = value.trade_cfg_id
            local config = self:GetTradeConfigByID(configID)
            if config then
                local duration = config.time
                totalTime = totalTime + duration
            end
        end
    end
    if totalTime == 0 then
        local configID = 201
        local config = self:GetTradeConfigByID(configID)
        if config then
            local duration = config.time
            totalTime = totalTime + duration
        end
    end
    train.end_timestamp = train.depart_timestamp + totalTime
end

--- 计算火车运输所需时间
--- @param train table 火车
function M:ComputeTrainTransportTime(train)
    if not train.train_quality then return end
    local totalTime = 0
    for _, value in ipairs(train.train_quality) do
        if value.part == TRADE_TRAIN_CARRIAGE.Head then
            local configID = value.trade_cfg_id
            local config = self:GetTradeConfigByID(configID)
            if config then
                local duration = config.time
                totalTime = totalTime + duration
            end
        end
    end
    if totalTime == 0 then
        local configID = 201
        local config = self:GetTradeConfigByID(configID)
        if config then
            local duration = config.time
            totalTime = totalTime + duration
        end
    end
    return totalTime
end

--- 根据记录 ID 获取火车数据
--- @param recordID number 记录 ID
function M:GetTrainDataByID(recordID)
    if self.trainLoadInfo.league_train then
        for _, value in ipairs(self.trainLoadInfo.league_train) do
            if value.record_id == recordID then
                return value
            end
        end
    end
end

--- 获取队伍总战力（羁绊加成）
--- @param battleList table 队伍
--- @return integer totalPower 总战力
function M:GetBattleListTotalPower(battleList)
    local teamList = battleList
    local totalPower = 0
    local countList = HeroManager:ComputeBattleListCountByKind(teamList)
    local _, _, activeValue = HeroManager:FindActiveBuffMaxNum(countList)
    for _, v in ipairs(teamList) do
        if not IsTableEmpty(v) then
            local heroVo = v
            local multNum = 1 + (activeValue / 10000)
            local heroPower = math.ceil(multNum * heroVo.power)
            totalPower = totalPower + heroPower
        end
    end
    return totalPower
end

--- 设置可掠夺的火车
--- @param trainData table 火车信息
function M:SetCanLootTrain(trainData)
    self.trainLoadInfo.can_loot_train = trainData
end

--- 清理可掠夺的火车
function M:ClearCanLootTrain()
    self.trainLoadInfo.can_loot_train = nil
end

--- 关闭界面
function M:ClosePanel()
    UI_CLOSE(UIDefine.UI_ActivityRankCenter)
    UI_CLOSE(UIDefine.UI_TradeWagonsWindow)
    UI_CLOSE(UIDefine.UI_TradeWagonsHelp)
    UI_CLOSE(UIDefine.UI_TradeWagonsHelpAll)
    UI_CLOSE(UIDefine.UI_TradeWagonsDepart)
    UI_CLOSE(UIDefine.UI_TradeWagonsDetail)
    UI_CLOSE(UIDefine.UI_TradeWagonsTrainDetail)
    UI_CLOSE(UIDefine.UI_TradeWagonsRecord)
    UI_CLOSE(UIDefine.UI_TradeWagonsRecordDetail)
    UI_CLOSE(UIDefine.UI_TradeWagonsRecordTrainDetail)
    UI_CLOSE(UIDefine.UI_TradeWagonsCarriage)
    UI_CLOSE(UIDefine.UI_SelectConductor)
    UI_CLOSE(UIDefine.UI_TradeWagonsTrainPassenger)
    UI_CLOSE(UIDefine.UI_TradeWagonsBuyContract)
    UI_CLOSE(UIDefine.UI_TradeWagonsShare)
    UI_CLOSE(UIDefine.UI_TradeWagonsShareConfirm)
    UI_CLOSE(UIDefine.UI_TradeWagonsTrainBattlePlan)
    UI_CLOSE(UIDefine.UI_TradeWagonsBattleDetail)
    UI_CLOSE(UIDefine.UI_TradeWagonsBattleTrainDetail)
end

--endregion -------------------------------------- 接口 -----------------------------------------

--region ----------------------------------------- 埋点 -----------------------------------------

--- 购买贸易合同
--- @param num number 购买数量
function M:TrackEventBuyContract(num)
    local thinkTable = {
        ["Trade_refresh_buy"] = num,  --（数值）刷新券购买
    }
    SdkHelper:ThinkingTrackEvent(ThinkingKey.Tradetruck, thinkTable)
end

--- 刷新货车品质
--- @param quality number 品质
function M:TrackEventRefreshWagon(quality)
    local thinkTable = {
        ["Trade_id_refresh"] = quality,  --（数值）运输货车刷新结果
    }
    SdkHelper:ThinkingTrackEvent(ThinkingKey.Tradetruck, thinkTable)
end

--- 货车发车
--- @param id number 货车 ID
--- @param rewards table 运输奖励
function M:TrackEventDepartWagon(id, rewards)
    local thinkTable = {
        ["Trade_id"] = id,  --（数值）运输货车 id
        ["Trade_reward"] = rewards and Json.encode(rewards) or {},  --（对象组）运输货车奖励
    }
    SdkHelper:ThinkingTrackEvent(ThinkingKey.Tradetruck, thinkTable)
end

--- 掠夺货车
--- @param playerID number 被劫玩家 ID
--- @param result boolean 被劫结果
--- @param rewards table 被劫奖励
function M:TrackEventPlunder(playerID, result, rewards)
    local thinkTable = {
        ["Trade_robbery_Role_id"] = playerID,  --（数值）运输货车被劫玩家ID
        ["Trade_robbery_result"] = result,     --（布尔）运输货车被劫结果
        ["Trade_robbery_reward"] = rewards and Json.encode(rewards) or {},  --（对象组）运输货车被劫奖励
    }
    SdkHelper:ThinkingTrackEvent(ThinkingKey.Tradetruck, thinkTable)
end

--- 刷新火车品质
--- @param trainID number 火车 ID
--- @param trainQuality table 火车车厢品质列表
--- @param trainReward table 火车车厢奖励列表
function M:TrackEventRefreshTrain(trainID, trainQuality, trainReward)

    table.sort(trainReward, function (a, b)
        if a.train_part ~= b.train_part then
            return a.train_part < b.train_part
        end
        return false
    end)

    local trainRewardList = {}
    for _, value in ipairs(trainReward) do
        local list = {}
        for _, reward in ipairs(value.rewards) do
            table.insert(list, {
                id = reward.reward.code,
                num = reward.reward.amount
            })
        end
        table.insert(trainRewardList, list)
    end

    local thinkTable = {
        ["Trade_train_id"] = trainID,                                                             --（数值）运输火车 ID
        ["Trade_train_id_refresh"] = trainQuality and Json.encode(trainQuality) or {},            --（对象组）运输火车刷新结果
        ["Trade_train_0_reward"] = trainRewardList[1] and Json.encode(trainRewardList[1]) or {},  --（对象组）运输火车车头奖励
        ["Trade_train_1_reward"] = trainRewardList[2] and Json.encode(trainRewardList[2]) or {},  --（对象组）运输火车车厢1奖励
        ["Trade_train_2_reward"] = trainRewardList[3] and Json.encode(trainRewardList[3]) or {},  --（对象组）运输火车车厢2奖励
        ["Trade_train_3_reward"] = trainRewardList[4] and Json.encode(trainRewardList[4]) or {},  --（对象组）运输火车车厢3奖励
        ["Trade_train_4_reward"] = trainRewardList[5] and Json.encode(trainRewardList[5]) or {},  --（对象组）运输火车车厢4奖励
    }
    SdkHelper:ThinkingTrackEvent(ThinkingKey.Tradetruck, thinkTable)
end

--- 排队上车
--- @param trainID number 火车 ID
--- @param trainCaptain table 列车长信息
--- @param trainPassenger table 乘客列表
function M:TrackEventQueueUpTrain(trainID, trainCaptain, trainPassenger)

    local passengerList = {}
    for _, value in ipairs(trainPassenger) do
        local list = {}
        for _, passenger in ipairs(value.passegers) do
            table.insert(list, passenger.id)
        end
        if value.train_part > 0 then
            passengerList[value.train_part] = list
        end
    end

    local thinkTable = {
        ["Trade_train_id"] = trainID,                                                        --（数值）运输火车 ID
        ["Trade_train_0_role"] = trainCaptain.id,                                            --（数值）运输火车车头_车长
        ["Trade_train_1_roles"] = passengerList[1] and Json.encode(passengerList[1]) or {},  --（对象组）运输火车车厢1_乘客
        ["Trade_train_2_roles"] = passengerList[2] and Json.encode(passengerList[2]) or {},  --（对象组）运输火车车厢2_乘客
        ["Trade_train_3_roles"] = passengerList[3] and Json.encode(passengerList[3]) or {},  --（对象组）运输火车车厢3_乘客
        ["Trade_train_4_roles"] = passengerList[4] and Json.encode(passengerList[4]) or {},  --（对象组）运输火车车厢4_乘客
    }
    SdkHelper:ThinkingTrackEvent(ThinkingKey.Tradetruck, thinkTable)
end

--- 掠夺火车
--- @param trainID number 火车 ID
--- @param robPlayerID number 进攻玩家 ID
--- @param result boolean 战斗结果
--- @param plunderReward table 掠夺获得的奖励
function M:TrackEventPlunderTrain(trainID, robPlayerID, result, plunderReward)

    table.sort(plunderReward, function (a, b)
        if a.train_part ~= b.train_part then
            return a.train_part < b.train_part
        end
        return false
    end)

    local plunderRewardList = {}
    for _, value in ipairs(plunderReward) do
        table.insert(plunderRewardList, {
            id = value.code,
            num = value.amount
        })
    end

    local thinkTable = {
        ["Trade_train_id"] = trainID,                                                                 --（数值）运输火车 ID
        ["Trade_robbery_Role_id"] = robPlayerID,                                                      --（数值）运输火车车头_车长
        ["Trade_robbery_result"] = result,                                                            --（布尔）运输火车被劫结果
        ["Trade_train_robbery_reward"] = plunderRewardList and Json.encode(plunderRewardList) or {},  --（对象组）运输火车被劫奖励_车头
    }
    SdkHelper:ThinkingTrackEvent(ThinkingKey.Tradetruck, thinkTable)
end

--- 召唤火车消耗道具数量
--- @param num number 消耗数量
function M:TrackEventCallTrainCost(num)
    local thinkTable = {
        ["Trade_train_call_cost"] = num,  --（数值）消耗道具数量
    }
    SdkHelper:ThinkingTrackEvent(ThinkingKey.Tradetruck, thinkTable)
end

--endregion -------------------------------------- 埋点 -----------------------------------------

return TradeWagonsManager