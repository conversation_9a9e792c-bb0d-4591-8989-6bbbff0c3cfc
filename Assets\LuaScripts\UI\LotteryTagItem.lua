---@class LotteryTagItem
local LotteryTagItem = {}

local prePath = "Assets/ResPackage/Prefab/UI/LotteryTagItem.prefab"
local pre
local redDotPre

local AssetPath = "Sprite/ui_slg_chouka/"

function LotteryTagItem:Create(parent,id,callBack)
    local item = {}
    setmetatable(item,{__index = LotteryTagItem})
	
	local logic = function()
		local newGo = CreateGameObjectWithParent(pre,parent)
		item.go = newGo
		item.go.name = "LotteryTagItem"..id
		SetActive(item.go,true)
		item.trans = newGo.transform
		item:Init()
		if callBack then
			callBack(item)
		end
	end

	local logic1 = function()
		if pre == nil then
			ResMgr:LoadAssetAsync(prePath,AssetDefine.LoadType.Instant,function(obj)
				pre = obj
				logic()
			end)
		else
			logic()
		end
	end

	logic1()
	----红点
	--local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "BeatRedDot")
	--if redDotPre == nil then
	--	ResMgr:LoadAssetAsync(assetPath,AssetDefine.LoadType.Instant,function(obj)
	--		redDotPre = obj
	--		logic1()
	--	end)
	--else
	--	logic1()
	--end
end

function LotteryTagItem:Init()
    local obj = self.go
	self.btn = GetChild(obj,"node/Btn",UEUI.Button)
	self.bg1 = GetChild(obj,"node/Btn/bg1",UEUI.Image)
	self.bg2 = GetChild(obj,"node/Btn/bg2",UEUI.Image)
	self.icon = GetChild(obj,"node/Btn/icon",UEUI.Image)
	self.mask1 = GetChild(obj,"node/Btn/mask1",UEUI.Image)
	self.mask2 = GetChild(obj,"node/Btn/mask2",UEUI.Image)
	self.txt = GetChild(obj,"node/Btn/txt",UEUI.Text)
	self.redPoint = GetChild(obj,"node/Btn/redDot")
	
	--local redDotParent = GetChild(obj,"node/Btn/redDot",UE.Transform)
	--self.redPoint = self:CreateRetDot(redDotParent)
	
	RemoveUIComponentEventCallback(self.btn,UEUI.Button)
    AddUIComponentEventCallback(self.btn,UEUI.Button,function(arg1,arg2)
        self:ClickItem(arg1,arg2)
    end)
end

function LotteryTagItem:SetItem(config)
	if nil == config then
		return
	end
	self.id = v2n(config.id)
	self.viewName = config.view
	--self.open = config.open
	local nameStr = v2n(config.name)
	
	self.txt.text = LangMgr:GetLang(nameStr)
	SetUIImage(self.icon,config.picture,false)
	
	local result = self:CheckUiItemRed()
	self:ShowActive(true)
end

--判断切页标签红点显隐
function LotteryTagItem:CheckUiItemRed()
	if not self:ShowActive() then
		return false
	end
	local isShow = false
	if self.viewName == UIDefine.UI_LotteryHero then
		if LotteryManager:GetFreeByType(DRAWCARD_TYPE.HERO,1) then
			SetActive(self.redPoint,true)
			return true
		end
		--isShow = BagManager:GetBagItemCount(ItemID.HeroTicket) > 0
	elseif self.viewName == UIDefine.UI_LotteryEquip then
		if LotteryManager:GetFreeByType(DRAWCARD_TYPE.EQUIP,1) then
			SetActive(self.redPoint,true)
			return true
		end
		--isShow = BagManager:GetBagItemCount(ItemID.EquipmentTicket) > 0
	end
	SetActive(self.redPoint,isShow) 
	return isShow
end

--设置标签是否解锁显示
function LotteryTagItem:ShowActive(log)
	local isShow = false
	if self.open then
		local openArry = string.split(self.open,"|")
		local type = v2n(openArry[1])
		local param = v2n(openArry[2])
		local level = NetUpdatePlayerData:GetLevel()
		if type == 1 then
			isShow = level >= param
		elseif type == 2 then
			local count  = NetMapNoteData:GetNoteCount(MAP_ID_MAIN,NetMapNoteData.ID.cloud_unlocked,param)
			isShow = count > 0
		end
	end
	isShow = true
    SetActive(self.go,isShow)
	return isShow
end

function LotteryTagItem:IsSelect(isOn)
	SetActive(self.bg1,not isOn)
	SetActive(self.bg2,isOn)
	SetActive(self.mask1,not isOn)
	SetActive(self.mask2,isOn)
    UnifyOutline(self.txt,isOn and "#A64A00" or "#005DAB")
end

function LotteryTagItem:TickUI(delta)
  
end

function LotteryTagItem:ClickItem(arg1,arg2)
	local actView = UIMgr:GetUIItem(UIDefine.UI_LotteryCenter)
	if nil ~= actView then
		actView:OnSwitchPage(self.id)
	end
end

function LotteryTagItem:OpenView()
	local actView = UIMgr:GetUIItem(self.viewName)
	if nil ~= actView and actView.isShow then
		return
	end
	
	UI_SHOW(self.viewName)
end

function LotteryTagItem:Close()
	local actView = UIMgr:GetUIItem(self.viewName)
	if nil ~= actView then
		if actView.isShow then
			actView:Close()
		end
	end
end

function LotteryTagItem:CloseNoAnima()
	local actView = UIMgr:GetUIItem(self.viewName)
	if nil ~= actView then
		if actView.isShow then
			UIMgr:Close(self.viewName)
		end
	end
end

function LotteryTagItem:CloseByAnima()
	local actView = UIMgr:GetUIItem(self.viewName)
	if nil ~= actView then
		if actView.isShow then
			actView:Close()
		end
	end
end

--创建红点
function LotteryTagItem:CreateRetDot(parent,callBack)
	local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "BeatRedDot")
	
	local logic = function(redDotObj)
		local obj = UEGO.Instantiate(redDotObj,parent)
		obj.transform.localPosition = Vector3.New(0,0,0)
		if callBack then
			callBack(obj)
		end
	end
	
	ResMgr:LoadAssetAsync(assetPath,AssetDefine.LoadType.Instant,function(obj)
		local redDotObj = ResMgr:LoadAssetSync(assetPath, AssetDefine.LoadType.Instant)
		logic(redDotObj)
	end)
end

--设置按钮是否可交互
function LotteryTagItem:SetInteractable(active)
	self.btn.interactable = active
end

return LotteryTagItem