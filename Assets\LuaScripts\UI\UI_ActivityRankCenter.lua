local UI_ActivityRankCenter = Class(BaseView)

---@type UIActRankItem
local uiItem = require("UI.UIActRankItem")
local bgMoveSpeed = 1
local bgOffset = 34

function UI_ActivityRankCenter:OnInit()
    self.config = ConfigMgr:GetData(ConfigDefine.ID.activity_rank)
    
    self.curViewName = ""--当前打开界面名称
    self.giftList = {}
    self.canStrongGiftTimer = false
    self.finalTime = 0
    self.ui.m_txtTimer.text = "00:00:00"
    SetActive(self.ui.m_btnStrongGift,false)
    StrongGiftManager:OnReqStrongGiftInfoReq(function(data)
        self:OnRefreshStrongGift()
    end)
end

function UI_ActivityRankCenter:OnCreate(param,isPush,openParam)
    self.ActItems = {}
    self.subTabPush = {}
    self.openParam = openParam
    
    self.bgRect = GetComponent(self.ui.m_rimgBg, UE.RectTransform)
    self.bgRect2 = GetComponent(self.ui.m_imgBg2, UE.RectTransform)
    self.firstBgRect = self.bgRect
    self.secondBgRect = self.bgRect2
    self.firstMoveX = self.firstBgRect.rect.width
    self.secondmoveX = self.secondBgRect.rect.width 
    self.firstPosition = self.ui.m_rimgBg.transform.localPosition
    self.secondPosition = self.bgRect2.transform.localPosition

    self.tabCanvas = GetComponent(self.ui.m_goAct,UE.Canvas)
    self.tabCanvas.sortingOrder = self.uiSortingOrder + 12
    SetActive(self.ui.m_rimgBg,false)
    local sortList = self:GetActivityGroupSortList()
    self.curLoadItem = 0
    self.totalLoadNum = #sortList
    for i = 1, #sortList do
        local config = sortList[i]
        self:AddUiItem(config.id)
    end
    self.isPush = isPush or false
    if self.isPush then
        self.pushIndex = param
    end
    self.showIndex = GetNumberValue(param,0)
    
    self:SetIsUpdateTick(true)
    
    self.canStrongGiftTimer = true
    self.timer = TimeMgr:CreateTimer(self, function()
       self:OnStrongGiftTimerLogic()

    end, 1)
end

function UI_ActivityRankCenter:LoadCompeleteCallBack()
    self:CheckIndex()
    self:ShowNowView()

    SetActive(self.ui.m_goAct, false);
    self:PlayTabAnim();
end

function UI_ActivityRankCenter:ChangeTabSortingOrder(value)
    if self.tabCanvas then
        self.tabCanvas.sortingOrder = value + 1
    end
end

function UI_ActivityRankCenter:GetActivityGroupSortList()
    local sortList = {}
    local config = self.config
    for i, v in ipairs(config) do
        table.insert(sortList,v)
    end
    table.sort(sortList, function(a, b)
        return a.sort < b.sort;
    end)
    return sortList
end

function UI_ActivityRankCenter:CheckIndex()
    if self.showIndex == 0 then
        self.showIndex = 1
        for k, v in pairs(self.ActItems) do
            if v:ShowActive() and v:CheckUiItemRed() then
                self.showIndex = tonumber(k)
                break
            end
        end
    end
end

function UI_ActivityRankCenter:OnRefresh(freshType,param)
    if freshType == 1 then
		local tabIndex = 0

		for k, v in pairs(self.ActItems) do
            if v:ShowActive() then
                -- 只赋值第一个打开的活动
                if tabIndex == 0 then
                    tabIndex = tonumber(k)
                end
            end
        end

		if tabIndex == 0 then
			self:Close()
			return
		else
            -- 关闭活动时 需要切换页签并且把自己关掉
            if param == self.showIndex then
                self.showIndex = tabIndex
                self:ShowNowView()
            end
        end
    elseif freshType == 2 then
        self:UpdateRedPoint()
    elseif freshType == 3 then
        -- 打开活动主界面时push了子界面
        local refreshIndex = param.refreshIndex
        local subTabPush = param.isPush
        if refreshIndex then
            self.subTabPush[refreshIndex] = subTabPush
        end
    elseif freshType == 4 then
        --刷新爬塔界面
        if self.showIndex == ACTIVITY_RANK_TABINDEX.TowerClimbing then
            for k, v in pairs(self.ActItems) do
                if k == self.showIndex then
                    v:RefreshView(param)
                end
            end
        end
    elseif freshType == 5 then
        self.curViewName =  param
        self:CheckStrongGiftActive()
    elseif freshType == 6 then
        self:OnRefreshStrongGift()
    end
end

function UI_ActivityRankCenter:onDestroy()
    if self.isPush then
        self.isPush = false
        self.pushIndex = nil
        NetPushViewData:RemoveViewByIndex(PushDefine.UI_ActivityRank, 1)
        NetPushViewData:CheckOtherView(true)
    else
        for pushIndex, v in pairs(self.subTabPush) do
            if self.showIndex == pushIndex then
                self:CheckSubPush(pushIndex)
            end
        end
        NetPushViewData:CheckOtherView(true)
        self.subTabPush = {}
    end
    for k, v in pairs(self.ActItems) do
        v:CloseNoAnima()
    end
    self.ActItems = nil
    
    if self.timer then
        TimeMgr:DestroyTimer(self,self.timer)
    end
    self.openParam = nil
end

function UI_ActivityRankCenter:AddUiItem(id)
    ---@type UiItem
    uiItem:Create(self.ui.m_goActs,id,function(item)
        item:SetItem(self.config[id], function(arg1, arg2)
            self:onUIEventClick(arg1, id)
        end)
        
        self.ActItems[id] = item
        local isShowItem = item:ShowActive()
        self.curLoadItem = self.curLoadItem + 1
        if self.curLoadItem >= self.totalLoadNum then
            self:LoadCompeleteCallBack()
        end
        --if isShowItem == false then
        --    if self.showIndex == id then
        --        self.showIndex = id + 1
        --    end
        --end
    end)
end

function UI_ActivityRankCenter:onUIEventClick(go,param)
    local name = go.name
    if name == "m_btnGoBack" then
        local item = self.ActItems[self.showIndex];
        if item ~= nil then
            if item.openType == 1 then
                if item.openParm == ActivityTotal.BowlingBattle then
                    if UIMgr:GetUIItem(UIDefine.UI_BowlingBattle, true) then
                        UI_UPDATE(UIDefine.UI_BowlingBattle, 34);
                        return;
                    end
                elseif item.openParm == ActivityTotal.OneToOne then
                    if UIMgr:GetUIItem(UIDefine.UI_OneToOneView, true) then
                        UI_UPDATE(UIDefine.UI_OneToOneView, 68);
                        return;
                    end
                elseif item.openParm == ActivityTotal.Rank then
                    if UIMgr:GetUIItem(UIDefine.UI_Rank, true) then
                        UI_UPDATE(UIDefine.UI_Rank, 3);
                        return;
                    end
                end
            end
        end
        self:Close()
    elseif name == "btn" then
        local index = param
        if self.showIndex == index then
            return
        end
        if index then
            self:SetShowIndex(index)
            self:ShowNowView()
        end
    elseif name == "m_btnStrongGift" then
        UI_SHOW(UIDefine.UI_StrengthenGift)
    end
end

function UI_ActivityRankCenter:SetShowIndex(index)
    if index then
        self.showIndex = index
    end
end

function UI_ActivityRankCenter:ShowNowView()
    --Close
    for k, v in pairs(self.ActItems) do
        if k == self.showIndex then
            v.btn.interactable = false
        else
            v.btn.interactable = true
            v:CloseNoAnima()
        end
    end
    --Open
    for k, v in pairs(self.ActItems) do
        if k == self.showIndex then
            --SetImageSprite(self.ui.m_rimgBg, v.config.bg, false);
            SetTexture(self.ui.m_rimgBg, v.config.bg,false,function() 
                SetActive(self.ui.m_rimgBg,true)
            end)
            SetImageSprite(self.ui.m_imgBg2, v.config.bg, false);
			if UIMgr:ViewIsShow(UIDefine.UI_ActivityRankCenter) then
                local subIsPush = self.pushIndex == k
				v:OpenView(subIsPush,self.openParam)
                self:CheckSubPush(k)
			end
        end
        v:IsSelect(k == self.showIndex)
    end
    SetActive(self.ui.m_imgBg2,self.showIndex == 8)
    if self.showIndex == 8 then
        self.canMoveBg = false
    else
        self.canMoveBg = false
        self.ui.m_rimgBg.transform.localPosition = self.firstPosition
        self.ui.m_imgBg2.transform.localPosition = self.secondPosition
    end
end

-- 切换页签时检查一下在打开界面时推送子界面的push缓存里有没有对应的push，有的话就清除
function UI_ActivityRankCenter:CheckSubPush(curShowIndex)
    local subPush = self.subTabPush[curShowIndex]
    if subPush then
        self.subTabPush[curShowIndex] = nil
        local actItem = self.ActItems[curShowIndex]
        if actItem then
            if actItem.openType == 1 then
                self:RemoveSubPushView(actItem.openParm)
            end
        end
    end
end

-- 移除推送
function UI_ActivityRankCenter:RemoveSubPushView(actType)
    local isOpen, activity = LimitActivityController:GetActiveIsOpenByTotal(actType)
    if isOpen and activity then
        local activeId = activity.info.activeId
        local pushList = NetPushViewData:GetPushViewListByType(PushDefine.UI_ActivityRank)
        local removeIndex 
        for i, v in ipairs(pushList) do
            if v2n(activeId) == v.activeId then
                removeIndex = i
            end
        end
        if removeIndex then
            NetPushViewData:RemoveViewByIndex(PushDefine.UI_ActivityRank, removeIndex)
        end
    end
end

function UI_ActivityRankCenter:PlayTabAnim()
    local tabList = self:GetActivityGroupSortList()
    local tweenIndex = 1
    for i, v in ipairs(tabList) do
        if self.showIndex == v.id then
            tweenIndex = i
        end
        local actItem = self.ActItems[v.id]
        if actItem then
            local aniNode = GetChild(actItem.go, "obj")
            aniNode.transform.localPosition = Vector3(1000, 0, 0);
        end
    end

    SetActive(self.ui.m_goAct, true);

    local totalHeight = 857;
    local cellHeight = 216;
    local offsetY = tweenIndex * cellHeight - totalHeight;
    --if offsetY > 0 then
    --    offsetY = self.ui.m_goActs.transform.localPosition.y + offsetY;
    --    DOLocalMoveY(self.ui.m_goActs.transform, offsetY, 0.2, nil, Ease.Linear);
    --end

    local animCount = 0
    for i, v in ipairs(tabList) do
        local actItem = self.ActItems[v.id]
        if actItem then
            local aniNode = GetChild(actItem.go, "obj")
            if actItem:ShowActive() then
                animCount = animCount + 1
                DOLocalMoveX(aniNode.transform, 0, animCount * 0.15, nil, Ease.OutQuad)
            else
                aniNode.transform.localPosition = Vector3(0, 0, 0);
            end
        end
    end
    --for k, v in pairs(self.ActItems) do
    --    local aniNode = GetChild(v.go, "obj")
    --    if v:ShowActive() then
    --        animCount = animCount + 1
    --        DOLocalMoveY(aniNode.transform, 0, animCount * 0.15, nil, Ease.OutQuad)
    --    else
    --        aniNode.transform.localPosition = Vector3(0, 0, 0);
    --    end
    --end
end

function UI_ActivityRankCenter:UpdateRedPoint()
    for k, v in pairs(self.ActItems) do
        v:CheckUiItemRed()
    end
end

function UI_ActivityRankCenter:TickUI(deltaTime)
    if self.canMoveBg then
        self:StartMoveBg(deltaTime)
    end
end

function UI_ActivityRankCenter:StartMoveBg(deltaTime)
    if self.firstMoveX ~= self.firstBgRect.rect.width then
        self.firstMoveX = self.firstBgRect.rect.width
    end
    if self.secondmoveX ~= self.secondBgRect.rect.width then
        SetUIPos(self.secondBgRect, self.secondBgRect.rect.width - bgOffset, 0)
        self.secondmoveX = self.secondBgRect.rect.width
    end
    -- 第二张背景图的初始位置
    if self.secondPosition.x ~= self.secondBgRect.rect.width - bgOffset then
        SetUIPos(self.bgRect2, self.secondBgRect.rect.width - bgOffset, 0)
        self.secondPosition = self.bgRect2.transform.localPosition
    end
    self:MoveBg(self.ui.m_rimgBg.transform,bgMoveSpeed,1,deltaTime)
    self:MoveBg(self.ui.m_imgBg2.transform,bgMoveSpeed,2,deltaTime)
end

function UI_ActivityRankCenter:MoveBg(rect,speed,type,deltaTime)
    local length = 0
    if type == 1 then
        length = -1*(self.firstMoveX) + bgOffset
    elseif type == 2 then
        length = -1*(self.secondmoveX) + bgOffset
    end
    local rectX = rect.anchoredPosition.x
    if rectX >= length then
        rect.transform:Translate(Vector3.left * speed * deltaTime)
    else
        self.firstBgRect,self.secondBgRect = self.secondBgRect, self.firstBgRect
        self:ChangeBgPositon(type)
    end
end

function UI_ActivityRankCenter:ChangeBgPositon(type)
    local firstRectX = self.firstBgRect.anchoredPosition.x - bgOffset
    if type == 1 then
        firstRectX = firstRectX + self.secondmoveX
    elseif type == 2 then
        firstRectX = firstRectX + self.firstMoveX
    end

    self.secondBgRect.anchoredPosition = Vector2.New(firstRectX,0)
end

--变强礼包倒计时
function UI_ActivityRankCenter:OnStrongGiftTimerLogic()
    if self.canStrongGiftTimer then
        local time = self.finalTime - TimeMgr:GetServerTime()
        if time <= 0 then
            self.canStrongGiftTimer = false
            self:OnRefreshStrongGift()
        else
            self.ui.m_txtTimer.text = TimeMgr:ConverSecondToString(time)
        end
    end
end

--刷新变强礼包
function UI_ActivityRankCenter:OnRefreshStrongGift()
    local list = StrongGiftManager:GetGiftList()
    local isEmpty = next(list) == nil
    self.canStrongGiftTimer =  not isEmpty
    self.giftList = list

    local gift = StrongGiftManager:GetQuickGift()
    if gift then
        self.finalTime = StrongGiftManager:GetFinalTime(gift.gift_id,gift.create_timestamp)
    end
    self:CheckStrongGiftActive()
    if self.canStrongGiftTimer then
        local time = self.finalTime - TimeMgr:GetServerTime()
        if time > 0 then
            self.ui.m_txtTimer.text = TimeMgr:ConverSecondToString(time)
        end
    end
    local canShow = self.curViewName == UIDefine.UI_LevelEnter
            --or self.curViewName == UIDefine.UI_JJcView
            or self.curViewName == UIDefine.UI_SlgTowerClimbingView
    
    SetActive(self.ui.m_btnStrongGift,not isEmpty and canShow)
end

--判断变强礼包入口显隐
function UI_ActivityRankCenter:CheckStrongGiftActive()
    local canShow = self.curViewName == UIDefine.UI_LevelEnter
            --or self.curViewName == UIDefine.UI_JJcView
            or self.curViewName == UIDefine.UI_SlgTowerClimbingView
    SetActive(self.ui.m_btnStrongGift,canShow and next(self.giftList) ~= nil)
end

return UI_ActivityRankCenter