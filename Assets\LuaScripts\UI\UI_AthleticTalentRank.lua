local UI_AthleticTalentRank = Class(BaseView)
local SlideRect = require("UI.Common.SlideRect")
local ItemBase = require("UI.Common.BaseSlideItem")
local RankItem = Class(ItemBase)
local ItemShowNums = 10
local SelectDay = 1

local DayTagSprite = {
    ["1_1"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind1_1.png",
    ["1_2"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind1_2.png",
    ["1_3"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind1_3.png",
    ["2_1"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind2_1.png",
    ["2_2"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind2_2.png",
    ["2_3"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind2_3.png",
    ["3_1"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind3_1.png",
    ["3_2"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind3_2.png",
    ["3_3"] = "Sprite/ui_huodongjingsai_jingjidaren/jingji_jind3_3.png",
}

local DayTagState = {
    Normal = 1,
    Selected = 2,
    Disable = 3,
}

function UI_AthleticTalentRank:OnInit()

end

function UI_AthleticTalentRank:OnCreate(serverData, isShowDayTag, isPush, pushParam, noRank)
    CreateCommonHead(GetChild(self.ui.m_goItemRank, "headNode",UE.Transform),0.4)
    self.myHeadNode = CreateCommonHead(GetChild(self.ui.m_goMyItemRank, "headNode",UE.Transform),0.5)
    
    local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.AthleticTalent)
    self.activityItem = activityItem

    self.rankListRect = GetComponent(self.ui.m_goRankScroll, UE.RectTransform)
    self:InitPanel()

    if self.ui.m_goParentTog.transform.childCount == 0 then
        self.dayTagList = {}
        self:GenerateDayToggleList()
    else
        self:RefreshDayToggleList()
    end

    local activityId
    local playerData
    local myselfData
    local pushDay

    if pushParam then
        activityId = pushParam[1]
        playerData = pushParam[2]
        myselfData = pushParam[3]
        pushDay = pushParam[4]
    end

    if pushDay == 7 then
        isShowDayTag = false
    end

    -- 展示日排行
    if isShowDayTag then
        self.ui.m_togDay.isOn = true
        local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
        if currentDay >= 7 then currentDay = 6 end
        SelectDay = currentDay
        SetActive(self.ui.m_txtDailyTime, true)
        SetActive(self.ui.m_txtTotalTime, false)
    -- 展示周排行
    else
        self.ui.m_togTotal.isOn = true
        SetActive(self.ui.m_txtDailyTime, false)
        SetActive(self.ui.m_txtTotalTime, true)
    end
    self:CheckNoRank(noRank)
    self.playAni = false
    self.isPush = isPush
    self.pushDay = pushDay
    if self.pushDay then
        SelectDay = self.pushDay
    end
    self.airIndex = 1

    local transform = self.uiGameObject.transform
    self.slider = SlideRect.new()
    self.slider:Init(transform:Find("m_imgBg/m_goRankScroll/ViewPort"):GetComponent(typeof(UEUI.ScrollRect)), 2)
    self.playerList = {}
    for i = 1, ItemShowNums do
        self.playerList[i] = RankItem.new()
        self.playerList[i]:Init(UEGO.Instantiate(self.ui.m_goItemRank.transform))
    end
    self.slider:SetItems(self.playerList, 5, Vector2.New(0, 0))
    self:RefreshScrollSize(isShowDayTag)

    if not noRank then
        -- 推送
        if self.isPush then
            if activityId then
                self.active = LimitActivityController:GetActiveMessage(activityId)
                self.playerListData = playerData
                self.myselfData = myselfData
                self.myRanking = myselfData.ranking
            end
            self.playAni = true
        else
            self.playerListData = serverData.ranking
            self.myselfData = serverData.player
        end
        if not self.myselfData.rank then return end

        Tween.Kill("AutoMoveFunc")
        local initIndex = self.myselfData.rank + 20
        local showNum = AthleticTalentManager:GetRankShowNum()
        if showNum then
            if self.myselfData.rank > showNum then
                initIndex = 1
            end
        end
        self.slider:SetData(self.playerListData, initIndex)
        self:SetMyself()
        -- 推送有奖励动画
        if self.playAni then
            local currentDay = self.pushDay
            local rankRewardConfig = AthleticTalentManager:GetRankRewardConfig(currentDay, v2n(self.myselfData.ranking))
            if rankRewardConfig then
                local rankReward = rankRewardConfig.reward
                local rewardT = {}
                rankReward = string.split(rankReward,";")
                for k, v in pairs(rankReward) do
                    local t = {}
                    local arr = string.split(v,"|")
                    t[1] = v2n(arr[1])
                    t[2] = v2n(arr[2])
                    table.insert(rewardT,t)
                end
                UI_SHOW(UIDefine.UI_ItemRankFinishReward, rewardT, 4)
            end
        end
        if isShowDayTag then
            UI_UPDATE(UIDefine.UI_AthleticTalentView, 1, self.myselfData.rank)
        else
            UI_UPDATE(UIDefine.UI_AthleticTalentView, 2, self.myselfData.rank)
        end
        self.slider:MoveToIndex(self.myselfData.rank, 2)

        local day = self.pushDay
        if day and day >= 7 then day = 6 end
        self:RefreshDayToggleList(day)
    end

    self:SetIsUpdateTick(true)
end

function UI_AthleticTalentRank:RefreshScrollSize(isShowDayTag)
    local height = isShowDayTag and 747 or 952
    self.rankListRect.sizeDelta = Vector2.New(925, height)
    GameUtil.ForceRebuildLayoutImmediate(self.rankListRect)
    if self.slider then
        self.slider:RefreshViewPort(self.uiGameObject.transform:Find("m_imgBg/m_goRankScroll/ViewPort"):GetComponent(typeof(UEUI.ScrollRect)), 2)
    end
end

    --- 初始化界面
function UI_AthleticTalentRank:InitPanel()
    -- 日排行页签
    self.ui.m_togDay.onValueChanged:RemoveAllListeners()
    self.ui.m_togDay.onValueChanged:AddListener(function (isOn)
        if isOn then
            --SetUIImage(self.ui.m_imgBg, "Sprite/ui_huodongjingsai/jingji_paihang_win.png", false)
            SetActive(self.ui.m_goParentTog, true)
            SetActive(self.ui.m_goTime, true)
            --self.ui.m_togDay.transform:SetAsLastSibling()
            --SetUISize(self.ui.m_goRankScroll, 1280, 452)
            --if self.slider then
            --    self.slider.viewportHeight = 452
            --end
            self:RefreshScrollSize(true)
            SetActive(self.ui.m_txtDailyTime, true)
            SetActive(self.ui.m_txtTotalTime, false)
            -- 默认打开当天的排行榜
            self:ShowCurrentDay()
        end
    end)
    -- 总排行页签
    self.ui.m_togTotal.onValueChanged:RemoveAllListeners()
    self.ui.m_togTotal.onValueChanged:AddListener(function (isOn)
        if isOn then
            --SetUIImage(self.ui.m_imgBg, "Sprite/ui_huodongjingsai/jingji_paihang_win2.png", false)
            SetActive(self.ui.m_goParentTog, false)
            SetActive(self.ui.m_goTime, false)
            --self.ui.m_togTotal.transform:SetAsLastSibling()
            --SetUISize(self.ui.m_goRankScroll, 1280, 576)
            --if self.slider then
            --    self.slider.viewportHeight = 576
            --end
            self:RefreshScrollSize(false)
            SetActive(self.ui.m_txtDailyTime, false)
            SetActive(self.ui.m_txtTotalTime, true)

            local function rankCallBack(data)
                self.playerListData = data.ranking
                self.myselfData = data.player
                SelectDay = 7
                self:RefreshScroll()
                self:SetMyself()
                UI_UPDATE(UIDefine.UI_AthleticTalentView, 2, self.myselfData.rank)
            end
            self.slider:SetData({})
            NetAthleticTalentData:RequestRankData(rankCallBack)
        end
    end)
end

function UI_AthleticTalentRank:ShowCurrentDay()
    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    local function rankCallBack(data)
        self.playerListData = data.ranking
        self.myselfData = data.player
        SelectDay = currentDay
        self:RefreshDayToggleList(currentDay)
        self:RefreshScroll()
        self:SetMyself()
        UI_UPDATE(UIDefine.UI_AthleticTalentView, 1, self.myselfData.rank)
    end
    -- 第七天结算时，点击日排行显示第六天的数据
    if currentDay >= 7 then currentDay = 6 end
    if 1 <= currentDay and currentDay <= 6 then
        NetAthleticTalentData:RequestRankData(rankCallBack, currentDay)
    end
end

--- 生成天数页签
function UI_AthleticTalentRank:GenerateDayToggleList()
    local toggleCount = 6
    for i = 1, toggleCount, 1 do
        local go = UEGO.Instantiate(self.ui.m_goTog.transform)
        go.transform:SetParent(self.ui.m_goParentTog.transform)
        go.transform.localScale = Vector3.New(1, 1, 1)
        SetActive(go, true)
        local goNormal = GetChild(go, "Normal")
        local goSelected = GetChild(go, "Selected")
        local goDisable = GetChild(go, "Disable")
        local imgNormal = GetChild(go, "Normal", UEUI.Image)
        local imgSelected = GetChild(go, "Selected", UEUI.Image)
        local imgDisable = GetChild(go, "Disable", UEUI.Image)
        SetActive(goNormal, false)
        SetActive(goSelected, false)
        SetActive(goDisable, true)
        local togNameNormal = GetChild(go, "Normal/togName", UEUI.Text)
        local togNameSelected = GetChild(go, "Selected/togName", UEUI.Text)
        local togNameDisable = GetChild(go, "Disable/togName", UEUI.Text)
        if 1 <= i and i <= 6 then
            togNameNormal.text = tostring(i)
            togNameSelected.text = tostring(i)
            togNameDisable.text = tostring(i)
        end
        local goCheck = GetChild(go, "check")
        local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
        local day = i
        local imgIndex = day
        if 2 <= imgIndex and imgIndex <= 6 then imgIndex = 2 end
        if imgIndex == 7 then imgIndex = 3 end
        local imgKeyNormal = imgIndex .. "_1"
        local imgKeySelected = imgIndex .. "_2"
        local imgKeyDisable = imgIndex .. "_3"
        SetUIImage(imgNormal, DayTagSprite[imgKeyNormal], true)
        SetUIImage(imgSelected, DayTagSprite[imgKeySelected], true)
        SetUIImage(imgDisable, DayTagSprite[imgKeyDisable], true)
        local button = go:GetComponent(typeof(UEUI.Button))
        button.onClick:AddListener(function ()
            if 1<= day and day <= 6 then
                currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
                if day <= currentDay then
                    local function rankCallBack(data)
                        self.playerListData = data.ranking
                        self.myselfData = data.player
                        SelectDay = day
                        self:RefreshDayToggleList(day)
                        self:RefreshScroll()
                        self:SetMyself()
                    end
                    NetAthleticTalentData:RequestRankData(rankCallBack, day)
                elseif day > currentDay then
                    UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(2203100))
                end
            end
        end)
        if day < currentDay then
            SetActive(goNormal, true)
            SetActive(goSelected, false)
            SetActive(goDisable, false)
        elseif day == currentDay then
            SetActive(goNormal, false)
            SetActive(goSelected, true)
            SetActive(goDisable, false)
        elseif day > currentDay then
            SetActive(goNormal, false)
            SetActive(goSelected, false)
            SetActive(goDisable, true)
        end
        -- 检查天数是否推送过，显示打勾图标
        local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")
        local isDayPush = NetAthleticTalentData:CheckDayPush(day)
        SetActive(goCheck, isEnterRank and isDayPush)
        local dayTag = {
            day = day,
            normal = goNormal,
            selected = goSelected,
            disable = goDisable,
            check = goCheck
        }
        table.insert(self.dayTagList, dayTag)
    end
end

--- 刷新天数页签
function UI_AthleticTalentRank:RefreshDayToggleList(day)
    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")
    Log.Info("竞技达人","刷新天数页签", currentDay)
    if currentDay >= 7 then currentDay = 6 end
    for _, value in ipairs(self.dayTagList) do
        if value.day < currentDay then
            self:ChangeDayTagState(value, DayTagState.Normal)
            if day and day == value.day then
                self:ChangeDayTagState(value, DayTagState.Selected)
            elseif day and day ~= value.day then
                self:ChangeDayTagState(value, DayTagState.Normal)
            end
        elseif value.day == currentDay then
            self:ChangeDayTagState(value, DayTagState.Selected)
            if day and day == value.day then
                self:ChangeDayTagState(value, DayTagState.Selected)
            elseif day and day ~= value.day then
                self:ChangeDayTagState(value, DayTagState.Normal)
            end
        elseif value.day > currentDay then
            self:ChangeDayTagState(value, DayTagState.Disable)
        end
        -- 检查天数是否推送过，显示打勾图标
        local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")
        local isDayPush = NetAthleticTalentData:CheckDayPush(value.day)
        SetActive(value.check, isEnterRank and isDayPush)
    end
end

--- 改变天数页签显示状态
--- @param value table 页签状态列表
--- @param state number 页签状态
function UI_AthleticTalentRank:ChangeDayTagState(value, state)
    if value and state then
        SetActive(value.normal, state == DayTagState.Normal)
        SetActive(value.selected, state == DayTagState.Selected)
        SetActive(value.disable, state == DayTagState.Disable)
    end
end

--- 检查是否未上榜
--- @param noRank boolean 未上榜
function UI_AthleticTalentRank:CheckNoRank(noRank)
    if not self.ui then return end
    -- 未上榜
    if noRank then
        SetActive(self.ui.m_goNoRankEnd, false)
        SetActive(self.ui.m_goNoRank, true)

        SetActive(self.ui.m_goMyItemRank, false)
        SetActive(self.ui.m_goRankScroll, false)

        local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.AthleticTalent)
        local listpoint
        if activityItem then
            local data = ConfigMgr:GetData(ConfigDefine.ID.rank_stage)
            for _, v in ipairs(data) do
                if v.rank_id == activityItem.form.id then
                    listpoint = v.listpoint
                    break
                end
            end
            local dailyScore = NetAthleticTalentData:GetDailyScore()
            local width = (dailyScore / listpoint) * 600
            SetUISize(self.ui.m_imgSlider2, math.min(width, 600), 50)
            if dailyScore > listpoint then dailyScore = listpoint end
            self.ui.m_txtProgress2.text = string.format("<color=\"#12FF00\">%s</color>/%s", dailyScore, listpoint)
            self.ui.m_txtNoRank.text = LangMgr:GetLangFormat(2203103, listpoint)
        end
    -- 已上榜
    else
        SetActive(self.ui.m_goNoRankEnd, false)
        SetActive(self.ui.m_goNoRank, false)
        SetActive(self.ui.m_goRankScroll, true)
        SetActive(self.ui.m_goMyItemRank, true)
    end
end

--- 刷新滚动视图
function UI_AthleticTalentRank:RefreshScroll()
    -- 已上榜
    local isEnterRank = NetAthleticTalentData:GetDataByKey("isEnterRank")

    if not self.myselfData.rank then
        self.myselfData.rank = 0
    end

    if self.myselfData.rank and isEnterRank then
        self:CheckNoRank(false)
        if self.slider then
            Tween.Kill("AutoMoveFunc")
            local initIndex = self.myselfData.rank + 20
            local showNum = AthleticTalentManager:GetRankShowNum()
            if showNum then
                if self.myselfData.rank > showNum then
                    initIndex = 1
                end
            end
            if self.playerListData then
                self.slider:SetData(self.playerListData, initIndex)
            else
                self.slider:SetData(self.myselfData, initIndex)
            end
            self.slider:MoveToIndex(self.myselfData.rank, 2)
        end
    -- 未上榜
    else
        self:CheckNoRank(true)
    end
end

function UI_AthleticTalentRank:OnRefresh(type)
    if type == 1 then
        SetActive(self.ui.m_goTarget,true)
        self:FlyMidToAir()
    end
end

function UI_AthleticTalentRank:FlyMidToAir(callBack)
    if nil == self.myselfData.ranking then
        return
    end
    local currentDay = self.pushDay
    local rankRewardConfig = AthleticTalentManager:GetRankRewardConfig(currentDay, v2n(self.myselfData.ranking))
    if rankRewardConfig then
        local rankReward = rankRewardConfig.reward
        local strArr = string.split(rankReward,";")
        for k, v in pairs(strArr) do
            local arr = string.split(v,"|")
            local itemId = v2n(NetSeasonActivity:GetChangeItemId(arr[1]))
            local num = v2n(arr[2])

            if self.airIndex <= 5 then
                local rewardGo      = GetChild(self.ui.m_goPanel,"pos" .. self.airIndex ,UEUI.Image)
                self.airIndex = self.airIndex + 1


                SetActive(rewardGo,true)
                SetImageSprite(rewardGo,ItemConfig:GetIcon(itemId),false)
            end
            local assetPath     = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "COM_FlyItem")
            ResMgr:LoadAssetAsync(assetPath, AssetDefine.LoadType.Instant,function(itemListRes)
                local endPos = GetUIPosByVector3(self.ui.m_goTarget.transform)
                local go            = UEGO.Instantiate(itemListRes)

                if go then
                    local goIcon = GetComponent(go,UEUI.Image)
                    go.transform:SetParent(self.ui.m_goMidPlant.transform)
                    go.transform.localPosition = Vector3(0,0,0)
                    go.transform.localScale = Vector3.New(1, 1, 1)
                    SetImageSprite(goIcon,ItemConfig:GetIcon(itemId),false)
                    SetUISize(go,100,100)
                    go.transform:SetParent(self.ui.m_goPanel.transform)
                    DOLocalMove(go.transform,Vector3(0,0,0),k*0.4,function ()
                        UEGO.Destroy(go)
                        if callBack then
                            callBack()
                        end
                        callBack = nil
                    end)
                end
            end)
        end
    end
end

function UI_AthleticTalentRank:onDestroy()
    self:SetIsUpdateTick(false)
    Tween.Kill("AutoMoveFunc")
    if self.isPush then
        NetPushViewData:RemoveViewByIndex(PushDefine.UI_AthleticTalentRank)
        NetPushViewData:CheckOtherView(true)
        if self.active then
            local currentDay = self.pushDay
            local isPushShow = NetAthleticTalentData:GetPushShow(currentDay)
            if not isPushShow then
                local rankRewardConfig = AthleticTalentManager:GetRankRewardConfig(currentDay, v2n(self.myRanking))
                if rankRewardConfig then
                    local rankReward = rankRewardConfig.reward
                    NetGlobalData:GetRewardToMap(rankReward,"UI_AthleticTalentRank")
                end
            end
            NetAthleticTalentData:SetPushShow(currentDay)

            if self.active:IsActivityEnd() then
                Log.Info("竞技达人","活动已结束，再次检查总榜 活动 id", self.active.info.activeId, "推送天数", self.pushDay)
                if self.pushDay >= 7 then
                    self.active:CloseActive()
                end
            end
        end
    end

    if self.playerList then
        for i = 1, #self.playerList do
            self.playerList[i]:onDestroy()
        end
    end
    self.playerList = nil
    self.slider = nil
    self.dayTagList = nil
    self.activityItem = nil
    self.myHeadNode = nil
end

function UI_AthleticTalentRank:onUIEventClick(go, param)
    local name = go.name
    if name == "m_btnClose" then
        self:Close()
    end
end

function UI_AthleticTalentRank:TickUI(deltaTime)
    local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.AthleticTalent)
    local currentDay = NetAthleticTalentData:GetDataByKey("currentDay")

    if 1 <= currentDay and currentDay <= 6 then
        local time = self:GetActiveTime()
        if time and time > 0 then
            self.ui.m_txtTotalTime.text = TimeMgr:BaseTime(time, 2, 2)
        else
            self.ui.m_txtTotalTime.text = LangMgr:GetLang(2203101)
        end
    elseif currentDay >= 7 then
        self.ui.m_txtTotalTime.text = LangMgr:GetLang(2203101)
    end

    if 1 <= currentDay and currentDay <= 6 then
        if activityItem then
            local endTime = AthleticTalentManager:GetEndTimeByDay(currentDay)
            if endTime then
                local remainTime = endTime - TimeZoneMgr:GetServerStampWithServerZone()
                if remainTime and remainTime > 0 then
                    self.ui.m_txtDailyTime.text = TimeMgr:BaseTime(remainTime, 2, 2)
                else
                    self.ui.m_txtDailyTime.text = LangMgr:GetLang(2203101)
                end
            end
        end
    elseif currentDay >= 7 then
        self.ui.m_txtDailyTime.text = LangMgr:GetLang(2203101)
    end

    -- 选择过去的天数，倒计时显示已结束
    if SelectDay < currentDay then
        self.ui.m_txtDailyTime.text = LangMgr:GetLang(2203101)
    end
end

--- 获取活动剩余时间
--- @return integer time 剩余时间
function UI_AthleticTalentRank:GetActiveTime()
    if not self.activityItem then return 0 end
    local condition = self.activityItem.info.state
    local time = 0
    if condition == 1 then
        time = self.activityItem:GetRemainingTime()
    elseif condition == 3 then
        time = self.activityItem:GetStartRemainingTime()
    elseif condition == 4 then
        time = self.activityItem:GetWaitTime()
    end
    return time
end

--- 设置自己的排名
function UI_AthleticTalentRank:SetMyself()
    if not self.ui then return end
    local rank = v2n(self.myselfData.rank)
    if not rank then return end
    if self.isPush then
        local ranking = v2n(self.myselfData.ranking)
        if ranking and ranking > 0 then
            rank = ranking
        end
    end
    -- 前三名的排名用图标显示
    if rank and rank <= 3 then
        local rankSprite
        if rank == 1 then
            rankSprite = "Sprite/ui_huodongjingsai/paihang_win4_icon1.png"
        elseif rank == 2 then
            rankSprite = "Sprite/ui_huodongjingsai/paihang_win4_icon2.png"
        elseif rank == 3 then
            rankSprite = "Sprite/ui_huodongjingsai/paihang_win4_icon3.png"
        end
        if rankSprite then
            SetUIImage(self.ui.m_imgRank, rankSprite, false)
            SetActive(self.ui.m_imgRank, true)
            SetActive(self.ui.m_txtRank, false)
        end
    else
        SetActive(self.ui.m_imgRank, false)
        SetActive(self.ui.m_txtRank, true)
    end
    -- 名字
    self.ui.m_txtName.text = NetUpdatePlayerData:GetPlayerInfo().name
    -- 头像
    --local playerHead = NetUpdatePlayerData:GetPlayerInfo().head
    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerHead)
    --if headConfig then
    --    SetUIImage(self.ui.m_imgHead, headConfig.icon, false)
    --end
    SetMyHeadAndBorderByGo(self.myHeadNode)
    
    -- 排名分数
    self.ui.m_txtRank.text = rank
    self.ui.m_txtScore.text = self.myselfData.point
    -- 奖励列表
    local currentDay = SelectDay
    local rankRewardConfig = AthleticTalentManager:GetRankRewardConfig(currentDay, rank)
    if rankRewardConfig then
        local rankReward = rankRewardConfig.reward
        local rewardT = string.split(rankReward,";")
        local length = table.count(rewardT)
        local itemCount = self.ui.m_goRewardList.transform.childCount
        self.m_rewardCellList = {}
        for i = 1, length do
            local cell = {}
            local obj
            if i <= itemCount then
                obj = self.ui.m_goRewardList.transform:GetChild(i - 1)
            else
                obj = UEGO.Instantiate(self.ui.m_goRewardItem, self.ui.m_goRewardList.transform)
            end
            local icon = GetChild(obj,"icon",UEUI.Image)
            local num = GetChild(obj,"num",UEUI.Text)
            local check = GetChild(obj, "check")
            cell["obj"] = obj
            cell["icon"] = icon
            cell["num"] = num
            cell["check"] = check
            table.insert(self.m_rewardCellList,cell)
        end

        if length < itemCount then
            for i = length, itemCount - 1, 1 do
                local cell = {}
                local obj = self.ui.m_goRewardList.transform:GetChild(i)
                local icon = GetChild(obj,"icon",UEUI.Image)
                local num = GetChild(obj,"num",UEUI.Text)
                local check = GetChild(obj, "check")
                cell["obj"] = obj
                cell["icon"] = icon
                cell["num"] = num
                cell["check"] = check
                table.insert(self.m_rewardCellList,cell)
            end
        end

        for k, v in pairs(self.m_rewardCellList) do
            if k <= length then
                local config = rewardT[k]
                local arr = string.split(config,"|")
                local itemId = v2n(NetSeasonActivity:GetChangeItemId(arr[1]))
                local num = v2n(arr[2])
                SetActive(v.obj,true)

                SetUIImage(v.icon, ItemConfig:GetIcon(itemId), false)
                v.num.text = "x" .. num

                local btn = v.icon.gameObject.transform:GetComponent(typeof(UEUI.Button))
                RemoveUIComponentEventCallback(btn, UEUI.Button)
                AddUIComponentEventCallback(btn, UEUI.Button, function()
                    if itemId > 0  then
                        -- 物品详情弹窗
                        UI_SHOW(UIDefine.UI_ItemTips, itemId)
                    end
                end)
                -- 检查天数是否推送过，显示打勾图标
                local isDayPush = NetAthleticTalentData:CheckDayPush(SelectDay)
                SetActive(v.check, isDayPush)
            else
                SetActive(v.obj,false)
            end
        end
        SetActive(self.ui.m_goRewardList, true)
        SetActive(self.ui.m_goNoRewardTip, false)
    else
        SetActive(self.ui.m_goRewardList, false)
        SetActive(self.ui.m_imgRank, false)
        SetActive(self.ui.m_txtRank, false)
        SetActive(self.ui.m_goNoRewardTip, true)
    end

    self.ui.m_scrollviewReward.horizontalNormalizedPosition = 0
end

function RankItem:OnInit(transform)
    self.trans      = transform
    self.bgImg      = GetChild(transform, "bg", UEUI.Image)
    self.img_rank   = GetChild(transform, "img_rank", UEUI.Image)
    self.scoreBg    = GetChild(transform, "scoreBg", UEUI.Image)
    self.img_head   = GetChild(transform, "head/img_head", UEUI.Image)
    self.txt_name   = GetChild(transform, "txt_name", UEUI.Text)
    self.txt_score  = GetChild(transform, "txt_score", UEUI.Text)
    self.txt_rank   = GetChild(transform, "txt_rank", UEUI.Text)
    self.scrollview = GetChild(transform, "scrollview", UEUI.ScrollRect)
    self.rewardItem = GetChild(transform, "rewardItem")
    self.customHeadObj      = GetChild(transform,"headNode/CustomHead")
    
    self.rewardCellList = {}
    self.rewardList = GetChild(transform, "scrollview/viewport/rewardList", UE.RectTransform)
end

function RankItem:UpdateData(data, index)
    if not data then return end
    -- 前三名的排名用图标显示
    if data.rank and data.rank <= 3 then
        local rankSprite
        if data.rank == 1 then
            rankSprite = "Sprite/ui_huodongjingsai/paihang_win4_icon1.png"
        elseif data.rank == 2 then
            rankSprite = "Sprite/ui_huodongjingsai/paihang_win4_icon2.png"
        elseif data.rank == 3 then
            rankSprite = "Sprite/ui_huodongjingsai/paihang_win4_icon3.png"
        end
        if rankSprite then
            SetUIImage(self.img_rank, rankSprite, false)
            SetActive(self.img_rank.gameObject, true)
        end
    else
        SetActive(self.img_rank.gameObject,false)
    end
    -- 名字
    self.txt_name.text = data.name
    -- 头像
    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, data.icon)
    --if headConfig then
    --    SetUIImage(self.img_head, headConfig.icon, false)
    --end

    SetHeadAndBorderByGo(self.customHeadObj,data.icon,data.border)
    self.customHeadObj.transform.localPosition = Vector3.zero
    
    -- 奖励列表
    local currentDay = SelectDay
    local rankRewardConfig = AthleticTalentManager:GetRankRewardConfig(currentDay, data.rank)
    if rankRewardConfig then
        local rankReward = rankRewardConfig.reward
        local rewardT = string.split(rankReward,";")
        local length = table.count(rewardT)
        local itemCount = self.rewardList.transform.childCount
        self.rewardCellList = {}

        for i = 1, length do
            local cell = {}
            local obj
            if i <= itemCount then
                obj = self.rewardList.transform:GetChild(i - 1)
            else
                obj = UEGO.Instantiate(self.rewardItem, self.rewardList.transform)
            end
            local icon = GetChild(obj,"icon",UEUI.Image)
            local num = GetChild(obj,"num",UEUI.Text)
            cell["obj"] = obj
            cell["icon"] = icon
            cell["num"] = num
            table.insert(self.rewardCellList, cell)
        end

        if length < itemCount then
            for i = length, itemCount - 1, 1 do
                local cell = {}
                local obj = self.rewardList.transform:GetChild(i)
                local icon = GetChild(obj,"icon",UEUI.Image)
                local num = GetChild(obj,"num",UEUI.Text)
                cell["obj"] = obj
                cell["icon"] = icon
                cell["num"] = num
                table.insert(self.rewardCellList,cell)
            end
        end

        for k, v in pairs(self.rewardCellList) do
            if k <= length then
                local config = rewardT[k]
                local arr = string.split(config,"|")
                local itemId = v2n(NetSeasonActivity:GetChangeItemId(arr[1]))
                local num = v2n(arr[2])
                SetActive(v.obj,true)

                SetUIImage(v.icon, ItemConfig:GetIcon(itemId), false)
                v.num.text = "x" .. num
                -- 玩家
                if data.playerId then
                    --v.num.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB("dc4232")
                    --v.num.gameObject:GetComponent(typeof(CS.Coffee.UIEffects.UIShadow)).effectColor = Color.HexToRGB("dc4232")
                    UnifyOutline(v.num.gameObject,"#c15600")
                -- 机器人
                else
                    --v.num.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB("0b76a0")
                    --v.num.gameObject:GetComponent(typeof(CS.Coffee.UIEffects.UIShadow)).effectColor = Color.HexToRGB("0b76a0")
                    UnifyOutline(v.num.gameObject,"#555cd4")
                end
                local btn = v.icon.gameObject.transform:GetComponent(typeof(UEUI.Button))
                RemoveUIComponentEventCallback(btn, UEUI.Button)
                AddUIComponentEventCallback(btn, UEUI.Button, function()
                    if itemId > 0  then
                        -- 物品详情弹窗
                        UI_SHOW(UIDefine.UI_ItemTips, itemId)
                    end
                end)
            else
                SetActive(v.obj,false)
            end
        end
        SetActive(self.rewardList, true)
    else
        SetActive(self.rewardList, false)
    end

    self.scrollview.horizontalNormalizedPosition = 0

    -- 玩家
    if data.playerId then
        --棕色
        SetUIImage(self.bgImg,"Sprite/ui_huodongjingsai_jingjidaren/baolingqiu_paihangbang_di2.png", false)
        SetUIImage(self.scoreBg,"Sprite/ui_huodongjingsai/jingji_paihang_dikuang3.png", false)
        self.txt_rank.text = string.format("<color=#dc4232>%s</color>", data.rank)
        self.txt_score.text = data.point--string.format("<color=#dc4232>%s</color>", data.point)
        --self.txt_name.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB("c65200")
        --self.txt_name.color = Color.HexToRGB("dc4232")
        UnifyOutline(self.txt_score.gameObject,"#c15600")
        UnifyOutline(self.txt_name.gameObject,"#c15600")
    -- 机器人
    else
        --蓝色
        self.txt_rank.text = string.format("<color=#0b76a0>%s</color>", data.rank)
        self.txt_score.text = data.point--string.format("<color=#0b76a0>%s</color>", data.point)
        SetUIImage(self.bgImg,"Sprite/ui_huodongjingsai_jingjidaren/baolingqiu_paihangbang_di1.png", false)
        SetUIImage(self.scoreBg,"Sprite/ui_huodongjingsai_jingjidaren/jingji_paihang_dikuang5.png", false)
        --self.txt_name.gameObject:GetComponent(typeof(UEUI.Outline)).effectColor = Color.HexToRGB("0382c4")
        --self.txt_name.color = Color.HexToRGB("0b76a0")
        UnifyOutline(self.txt_score.gameObject,"#555cd4")
        UnifyOutline(self.txt_name.gameObject,"#555cd4")
    end
end

function RankItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function RankItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function RankItem:onDestroy()
    UEGO.Destroy(self.trans.gameObject)
    self.trans = nil
    self.bgImg = nil
    self.img_rank = nil
    self.scoreBg = nil
    self.img_head = nil
    self.txt_name = nil
    self.txt_score = nil
    self.txt_rank = nil
    self.rewardCellList = nil
    self.customHeadObj = nil
end

return UI_AthleticTalentRank