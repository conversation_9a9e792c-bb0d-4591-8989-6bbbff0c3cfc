local UI_BattleBuyPassPort = Class(BaseView)
local ItemBase = require("UI.Common.BaseSlideItem")
local rewardItem = Class(ItemBase)
function UI_BattleBuyPassPort:OnInit()
    
end

function UI_BattleBuyPassPort:OnCreate(passportID,isHigh)
	self.passportID = passportID or 101001
	self.showIndex = isHigh and 2 or 1
	self.curLevel = BattlePassManager:GetCurPassPortLevel2(self.passportID)
	self.normal_reward,self.mid_reward,self.high_reward = BattlePassConfig:GetPassRewardById(self.passportID)
	self.data = BattlePassConfig:GetBpDataById(passportID)
	local new_mid_reward = {}
	for k, v in pairs(self.mid_reward) do
		local arr = string.split(v,";")
		for k1, v1 in pairs(arr) do
			local arr2 = string.split(v1,"|")
			local item = {}
			item["id"] = v2n(arr2[1])
			item["count"] = v2n(arr2[2])
			item["level"] = k
			item["isCore"] = self:IsCoreReward(item["id"])
			table.insert(new_mid_reward,item)
		end
	end
	self.mid_reward = new_mid_reward
	local new_high_reward = {}
	for k, v in pairs(self.high_reward) do
		local arr = string.split(v,";")
		for k1, v1 in pairs(arr) do
			local arr2 = string.split(v1,"|")
			local item = {}
			item["id"] = v2n(arr2[1])
			item["count"] = v2n(arr2[2])
			item["level"] = k
			item["isCore"] = self:IsCoreReward(item["id"])
			table.insert(new_high_reward,item)
		end
	end
	self.high_reward = new_high_reward
	self.isOnly = false
	if BattlePassManager:IsBuyMidPassPort(self.passportID) or BattlePassManager:IsBuyHighPassPort(self.passportID) then
		self.isOnly = true
	end
	self:InitReward()
	self:ClickTab()

	SetActive(self.ui.m_goTipContent, self.data.function_type == 3)
end

function UI_BattleBuyPassPort:GetCurLevelRewar(data)
	local curData = {}
	for k, v in pairs(data) do
		if self.curLevel >= v.level then
			table.insert(curData,v)
		end
	end
	return curData
end

function UI_BattleBuyPassPort:MergeReward(rewardsP)
	local rewards = DeepCopy(rewardsP)
	local new_rewards = {}
	for k, v in pairs(rewards) do
		local isAdd = true
		for k1, v1 in pairs(new_rewards) do
			if v1.id == v.id then
				v1.count = v1.count + v.count
				isAdd = false
				break
			end
		end
		if isAdd then
			table.insert(new_rewards,v)
		end
	end
	return new_rewards
end

--是否是核心奖励 需要特效和排序 处理
function UI_BattleBuyPassPort:IsCoreReward(id)
	local core = BattlePassConfig:GetCoreReward(self.passportID)
	for i, itemID in ipairs(core) do
		if id == v2n(itemID) then
			return i
		end
	end
	return nil
end

function UI_BattleBuyPassPort.SortItem(a,b)
	if a.isCore and b.isCore then
		return a.isCore < b.isCore
	elseif a.isCore and not b.isCore then
		return true
	elseif not a.isCore and b.isCore then
		return false
	end
end

function UI_BattleBuyPassPort:InitReward()

	CollectCardManager:SetMaskSize(self.ui.m_goItem,self.ui.m_goParticleMask)

	local transform = self.uiGameObject.transform
	if nil == self.mid_slider then
		self.mid_slider = require("UI.Common.SlideRect").new()
		self.mid_slider:Init(transform:Find("bg/m_goTopRewardList/viewPort"):GetComponent(typeof(UEUI.ScrollRect)),1)

		local task_items = {}
		local itemTrans = self.ui.m_goItem.transform
		for i=1,9 do
			task_items[i] = rewardItem.new()
			task_items[i]:Init(UEGO.Instantiate(itemTrans),true)
		end
		self.mid_slider:SetItems(task_items,-20,Vector2.New(0,0))
		local data = self:GetCurLevelRewar(self.showIndex == 1 and self.mid_reward or self.high_reward)
		data = self:MergeReward(data)
		table.sort(data,self.SortItem)
		self.mid_slider:SetData(data)
		SetActive(self.ui.m_goTopEmpty,#data <= 0)
	elseif self.mid_slider and self.mid_reward then
		local data = self:GetCurLevelRewar(self.showIndex == 1 and self.mid_reward or self.high_reward)
		data = self:MergeReward(data)
		table.sort(data,self.SortItem)
		self.mid_slider:SetData(data)
		SetActive(self.ui.m_goTopEmpty,#data <= 0)
	end
	
	if nil == self.high_slider then
		self.high_slider = require("UI.Common.SlideRect").new()
		self.high_slider:Init(transform:Find("bg/m_goUnderRewardList/viewPort"):GetComponent(typeof(UEUI.ScrollRect)),1)

		local task_items = {}
		local itemTrans = self.ui.m_goItem.transform

		for i=1,9 do
			task_items[i] = rewardItem.new()
			task_items[i]:Init(UEGO.Instantiate(itemTrans),true)
		end
		self.high_slider:SetItems(task_items,-20,Vector2.New(0,0))
		local data = self.showIndex == 1 and self.mid_reward or self.high_reward
		data = self:MergeReward(data)
		table.sort(data,self.SortItem)
		self.high_slider:SetData(data)
		SetActive(self.ui.m_goUnderEmpty,#data <= 0)
	elseif self.mid_slider and self.high_reward then
		local data = self.showIndex == 1 and self.mid_reward or self.high_reward
		data = self:MergeReward(data)
		table.sort(data,self.SortItem)
		self.high_slider:SetData(data)
		SetActive(self.ui.m_goUnderEmpty,#data <= 0)
	end
end

function UI_BattleBuyPassPort:OnRefresh(param)
    
end

function UI_BattleBuyPassPort:onDestroy()
	self.mid_slider = nil
	self.high_slider = nil
	self.mid_payId = nil
	self.high_payId = nil
end

function UI_BattleBuyPassPort:ClickTab(index)
	if index then
		if self.showIndex == index then
			return
		end
		self.showIndex = index
	end
	SetActive(self.ui.m_gotab,not self.isOnly)
	SetActive(self.ui.m_goOnlyTab,self.isOnly)
	SetActive(self.ui.m_goOnlyMid,self.showIndex == 1)
	SetActive(self.ui.m_goOnlyHigh,self.showIndex == 2)
	if self.showIndex then
		if self.showIndex == 1 then
			SetActive(self.ui.m_goMid,true)
			SetActive(self.ui.m_goHigh,false)
			SetActive(self.ui.m_goSelectMidTab,true)
			SetActive(self.ui.m_goSelectHighTab,false)
			
			local txtValue = self.data.price_value
			txtValue = string.split(txtValue,"|")
			txtValue = txtValue[1]
			self.ui.m_txtValue.text = txtValue.."%!!"
			
			local price = self.data.payment_id
			price = string.split(price,"|")
			self.mid_payId = v2n(price[1])
			local payConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.payment,self.mid_payId)
			self.ui.m_txtPrice.text = LangMgr:GetLang(payConfig.price_langid)
			local buyBtn = GetChild(self.uiGameObject,"bg/buyBtn")
			PurchaseManager:CreateScoreTag(self.mid_payId,buyBtn.transform)
		else
			SetActive(self.ui.m_goMid,false)
			SetActive(self.ui.m_goHigh,true)
			SetActive(self.ui.m_goSelectHighTab,true)
			SetActive(self.ui.m_goSelectMidTab,false)	
			
			local txtValue = self.data.price_value
			txtValue = string.split(txtValue,"|")
			txtValue = txtValue[2]
			self.ui.m_txtValue.text = txtValue.."%!!"	
			
			local price = self.data.payment_id
			price = string.split(price,"|")
			self.high_payId = v2n(price[2])
			local payConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.payment,self.high_payId)
			self.ui.m_txtPrice.text = LangMgr:GetLang(payConfig.price_langid)
			
			local buyBtn = GetChild(self.uiGameObject,"bg/buyBtn")
			PurchaseManager:CreateScoreTag(self.high_payId,buyBtn.transform)
			
			local spine = GetChild(self.ui.m_goSpine,"spine",CS.Spine.Unity.SkeletonGraphic)			
			spine:Initialize(true)
			SetSpineAnim(spine,"idle",1)
		end
	end
end

function UI_BattleBuyPassPort:onUIEventClick(go,param)
    local name = go.name
	if name == "m_goMidTab" then
		self:ClickTab(1)
		self:InitReward()
	elseif name == "m_goHighTab" then
		self:ClickTab(2)
		self:InitReward()
	elseif name == "buyBtn" then
		local price = self.data.payment_id
		price = string.split(price,"|")
		if self.showIndex == 1 then
			PaymentConfig:ShowPay(v2n(price[1]))
		elseif self.showIndex == 2 then
			PaymentConfig:ShowPay(v2n(price[2]))
		end
	elseif name == "m_btnClose" then
		self:Close()
	end
end

function rewardItem:OnInit(transform)
	self.quality = transform:Find("quality"):GetComponent(typeof(UEUI.Image))
	self.icon = transform:Find("icon"):GetComponent(typeof(UEUI.Image))
	self.num = transform:Find("num"):GetComponent(typeof(UEUI.Text))
	self.btn = transform:GetComponent(typeof(UEUI.Button))
	self.effect = GetChild(transform,"effect")
end

function rewardItem:UpdateData(data,index)
	if data then

		SetActive(self.effect,data.isCore)

		SetUIImage(self.icon,ItemConfig:GetIcon(data.id),false)
		self.num.text = "x"..NumToGameString(data.count)
		
		local quality = ItemConfig:GetSlgQuality(data.id)
		SetUIImage(self.quality,BAGITEM_QUALITY_PATH[quality],false)
		
		if self.btn then
			RemoveUIComponentEventCallback(self.btn, UEUI.Button)
			AddUIComponentEventCallback(self.btn, UEUI.Button, function(arg1, arg2)
				UI_SHOW(UIDefine.UI_ItemTips,data.id)
			end)
		end
	end
end

function rewardItem:UpdatePosition(vec)
	self.rectTrans.anchoredPosition = vec
end

function rewardItem:GetAnchoredPositon()
	return self.rectTrans.anchoredPosition
end

return UI_BattleBuyPassPort