local UI_BattleBuyPassPortModel = {}

UI_BattleBuyPassPortModel.config = {["name"] = "UI_BattleBuyPassPort", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = true, ["anim"] = 0,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_BattleBuyPassPortModel:Init(c)
    c.ui = {}    
    c.ui.m_goMid = GetChild(c.uiGameObject,"bg/m_goMid")
    c.ui.m_goHigh = GetChild(c.uiGameObject,"bg/m_goHigh")
    c.ui.m_goSpine = GetChild(c.uiGameObject,"bg/m_goHigh/m_goSpine")
    c.ui.m_txtValue = GetChild(c.uiGameObject,"bg/Image (1)/m_txtValue",UEUI.Text)
    c.ui.m_goTipContent = GetChild(c.uiGameObject,"bg/m_goTipContent")
    c.ui.m_goTopRewardList = GetChild(c.uiGameObject,"bg/m_goTopRewardList")
    c.ui.m_goTopEmpty = GetChild(c.uiGameObject,"bg/m_goTopRewardList/m_goTopEmpty")
    c.ui.m_goUnderRewardList = GetChild(c.uiGameObject,"bg/m_goUnderRewardList")
    c.ui.m_goUnderEmpty = GetChild(c.uiGameObject,"bg/m_goUnderRewardList/m_goUnderEmpty")
    c.ui.m_gotab = GetChild(c.uiGameObject,"bg/m_gotab")
    c.ui.m_goMidTab = GetChild(c.uiGameObject,"bg/m_gotab/m_goMidTab")
    c.ui.m_goSelectMidTab = GetChild(c.uiGameObject,"bg/m_gotab/m_goMidTab/m_goSelectMidTab")
    c.ui.m_goHighTab = GetChild(c.uiGameObject,"bg/m_gotab/m_goHighTab")
    c.ui.m_goSelectHighTab = GetChild(c.uiGameObject,"bg/m_gotab/m_goHighTab/m_goSelectHighTab")
    c.ui.m_goOnlyTab = GetChild(c.uiGameObject,"bg/m_goOnlyTab")
    c.ui.m_goOnlyMid = GetChild(c.uiGameObject,"bg/m_goOnlyTab/m_goOnlyMid")
    c.ui.m_goOnlyHigh = GetChild(c.uiGameObject,"bg/m_goOnlyTab/m_goOnlyHigh")
    c.ui.m_txtPrice = GetChild(c.uiGameObject,"bg/buyBtn/m_txtPrice",UEUI.Text)
    c.ui.m_goItem = GetChild(c.uiGameObject,"bg/m_goItem")
    c.ui.m_goParticleMask = GetChild(c.uiGameObject,"bg/m_goParticleMask")
    c.ui.m_btnClose = GetChild(c.uiGameObject,"bg/m_btnClose",UEUI.Button)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_BattleBuyPassPortModel