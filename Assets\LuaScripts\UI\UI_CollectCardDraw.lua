local UI_CollectCardDraw = Class(BaseView)

function UI_CollectCardDraw:OnInit()
    self.isPlayAni = true;
    self.isClickSkip = false;
    self.isPlayCloseAni = false;
end

function UI_CollectCardDraw:OnCreate(param, isPush)
    self.isPush = isPush
    if param and param.type == 1 then
        local m_txtAuto204 = GetChild(self.uiGameObject, "bg/Top/m_txtAuto204");
        SetActive(m_txtAuto204, false);
        local textbg = GetChild(self.uiGameObject, "bg/textbg");
        SetActive(textbg, false);
        local m_txtAuto32 = GetChild(self.uiGameObject, "bg/m_txtAuto32");
        SetActive(m_txtAuto32, false);
    end

    self.cardPackReward = NetCollectCardData:GetCardPackRewardList()

    if IsTableEmpty(self.cardPackReward) then
        return
    end

    self.index = 1
    if param and param.itemId then
        local index = NetCollectCardData:GetCardPackRewardIdx(param.itemId);
        if index then
            self.index = index;
        end
    end

    if self.index <= #self.cardPackReward then
        local reward = self.cardPackReward[self.index]
        local result = reward.result
        self.rewardList = string.split(result, "|")
        self.cardCount = #self.rewardList

        local spritePath = ItemConfig:GetIcon(reward.packID);
        local replaceStr = string.gsub(spritePath, "_2", "");
        local icon = GetChild(self.ui.m_goPack, "item/body", UEUI.Image);
        SetUIImage(icon, replaceStr, false);

        replaceStr = string.gsub(spritePath, "_2", "_1");
        icon = GetChild(self.ui.m_goPack, "item/top", UEUI.Image);
        SetUIImage(icon, replaceStr, false);

        local parent = GetChild(self.ui.m_goPack, "item/Particle System", UE.Transform);
        local childCount = parent.childCount;
        local child;
        local index = reward.packID - 60000;
        for i = 1, childCount do
            child = parent:GetChild(i - 1);
            if child then
                SetActive(child, i == index);
            end
        end
    end

    for i = 1, self.cardCount, 1 do
        local child = self.ui.m_transCardPos:GetChild(i - 1)
        SetActive(child, true)
    end

    -- local collectionPos = LimitActivityController:GetCollectionPos();
    -- if collectionPos then
    --     self.ui.m_goCollect.transform.localPosition = Vector3(collectionPos[1], collectionPos[2], 0);
    -- end

    self.cardIndex = 1
    self.cardShowIndex = 1
    self.cardMaxIndex = self.cardCount;
    self.isRecycle = false
    self:PlayDrawCardAnim()

    local view = UIMgr:GetUIItem(UIMgr:GetNowMainUI())
    if view and view.ui then
        local targetGo = view.ui["m_goCollect"]
        local targetGoPos = UIRectPosFit(targetGo)
        local rect = GetComponent(self.ui.m_goCollect, UE.RectTransform)
        rect.anchoredPosition = Vector2.New(targetGoPos[1], targetGoPos[2])
    end
end

function UI_CollectCardDraw:OnRefresh(param)
    
end

function UI_CollectCardDraw:onDestroy()
    UI_UPDATE(UIDefine.UI_CollectCardView)
    UI_UPDATE(UIDefine.UI_CollectCardDetail)
    UI_UPDATE(UIDefine.UI_CollectCardSingle)
    
    TimeMgr:DestroyTimer(UIDefine.UI_CollectCardDraw);

    if UIMgr:ViewIsShow(UIDefine.UI_CollectCardGift) then
        if self.index then
            NetCollectCardData:removeCardPackReward(self.index);
        end
        UI_UPDATE(UIDefine.UI_CollectCardGift, 1);
    else
        NetCollectCardData:removeCardPackReward(1)
    end

    if self.isPush then
        NetPushViewData:RemoveViewByIndex(PushDefine.UI_CollectCardDraw)
        NetPushViewData:CheckOtherView(true)
    end

    local cardPackRewardList = NetCollectCardData:GetCardPackRewardList()
    if IsTableEmpty(cardPackRewardList) then
        NetCollectCardData:TriggerEventList()
    end
end

function UI_CollectCardDraw:onUIEventClick(go,param)
    local name = go.name
    -- 关闭
    if name == "m_btnClose" then
        self:Close()
    elseif name == "m_btnBack" then
        -- self:ShowReward()
    end
end

function UI_CollectCardDraw:AutoClose()
    if self.isPlayAni then
        if not self.isClickSkip then
            self.isClickSkip = true;
            self:SkipCardAnim();
        end
    else
        self:OnPlayCloseAni();
    end
end

--- 播放抽卡动画
function UI_CollectCardDraw:PlayDrawCardAnim()
    local cardCount = self.cardCount

    local packAnimName = "cardpack"
    if cardCount > 3 then
        if self.isRecycle then
            packAnimName = "cardpack4"
        else
            packAnimName = "cardpack3"
        end
    elseif cardCount == 3 then
        if self.isRecycle then
            packAnimName = "cardpack5"
        else
            packAnimName = "cardpack2"
        end
    elseif cardCount == 2 then
        packAnimName = "cardpack"
    end

    self.packAnim = GetChild(self.ui.m_goPack, "item", UE.Animation)

    ResetAnimation(self.packAnim, packAnimName)

    PlayAnimStatusIndex(self.packAnim, packAnimName, function ()
        self:CreateCardItem()
        if packAnimName == "cardpack3" or packAnimName == "cardpack4" then
            self.cardCount = self.cardCount - 1
            self.isRecycle = true
            self:PlayDrawCardAnim()
        end
    end, function ()
        self:CreateCardItem()
    end, function ()
        self:CreateCardItem()
    end)
end

--- 创建卡牌
function UI_CollectCardDraw:CreateCardItem()
    if self.cardIndex > self.cardMaxIndex then return end

    local cardItem = CreateGameObjectWithParent(self.ui.m_goCardItem, self.ui.m_transCard)

    local cardID = v2n(self.rewardList[self.cardIndex])
    local isGold = NetCollectCardData:IsGoldCard(cardID)

    local item = GetChild(cardItem, "item")
    -- 背景
    local bg = GetChild(item, "front/bg", UEUI.Image)
    if isGold then
        SetUIImage(bg, "Sprite/ui_tujian/jika_ka2.png", false)
    else
        SetUIImage(bg, "Sprite/ui_tujian/jika_ka1.png", false)
    end
    -- 标题
    local title = GetChild(item, "front/titleBg/title", UEUI.Text)
    title.text = ItemConfig:GetLangByID(cardID)
    -- 图标
    local icon = GetChild(item, "front/icon", UEUI.Image)
    local iconPath = ItemConfig:GetIcon(cardID)
    SetUIImage(icon, iconPath, false)
    -- 星级
    local starIcon = GetChild(item, "front/starIcon", UEUI.Image)
    CollectCardManager:SetStar(cardID,starIcon)
    SetActive(cardItem, true)
    -- 红点
    local redPoint = GetChild(item, "front/redPoint", UEUI.Image)
    local hasRedPoint = NetCollectCardData:GetRedPoint(cardID)
    if hasRedPoint then
        SetActive(redPoint, true)
    else
        SetActive(redPoint, false)
    end

    if not self.isClickSkip then
        local cardAnim = GetChild(cardItem, "item", UE.Animation)
        local animName = "drawcard_1"
        if isGold then
            animName = "drawcard"
        end
        PlayAnimStatusIndex(cardAnim, animName, function ()
            if not self.isClickSkip then
                local child = self.ui.m_transCardPos:GetChild(self.cardShowIndex - 1)
                local pos = UIRectPosFit(child)
                DOScale(cardItem.transform, Vector3.New(0.8, 0.8, 1), 0.2)
                local rect = GetComponent(cardItem, UE.RectTransform)
                DOAnchorPos(rect, Vector2.New(pos[1], pos[2], 0), 0.2)
                self.cardShowIndex = self.cardShowIndex + 1
            end
        end)
    else
        self:SetDefaultCardItem(self.cardShowIndex, isGold);
        self.cardShowIndex = self.cardShowIndex + 1
    end

    if self.cardMaxIndex == self.cardIndex then
        SetActive(self.ui.m_goPack, false);
        self:OnDelayClose();
    end

    self.cardIndex = self.cardIndex + 1
	self:SortOrderAllCom(true)
end

--- 展示奖励
function UI_CollectCardDraw:ShowReward()
    if self.index <= #self.cardPackReward then
        local reward = self.cardPackReward[self.index]
        local result = reward.result
        local rewardList = string.split(result, "|")

        self.ui.m_txtRewardTip.text = string.format("你已收到 %s 张卡牌", #rewardList)

        local rewardItemCount = self.ui.m_transCardList.transform.childCount
        -- 先全部隐藏
        for i = 1, rewardItemCount, 1 do
            local item = self.ui.m_transCardList.transform:GetChild(i - 1)
            SetActive(item, false)
        end
        -- 再根据奖励列表显示
        for key, value in ipairs(rewardList) do
            local cardID = v2n(value)
            local item
            -- 有可用的 item 直接获取
            if key <= rewardItemCount then
                item = self.ui.m_transCardList.transform:GetChild(key - 1)
            -- item 不够用，创建新的
            else
                item = CreateGameObjectWithParent(self.ui.m_goCardItem, self.ui.m_transCardList)
            end
            -- 标题
            local title = GetChild(item, "Image/title", UEUI.Text)
            title.text = cardID
            -- 图标
            local icon = GetChild(item, "icon", UEUI.Image)
            local iconPath = ItemConfig:GetIcon(cardID)
            SetUIImage(icon, iconPath, false)
            -- 星级
            local starIcon = GetChild(item, "bg/starIcon", UEUI.Image)
            CollectCardManager:SetStar(cardID,starIcon)
            
            -- 品质
            local quality = GetChild(item, "quality")
            SetActive(quality, NetCollectCardData:IsGoldCard(cardID))
            SetActive(item, true)
        end
    end
    -- 检查下一个奖励
    self.index = self.index + 1
    if self.index <= #self.cardPackReward then
        SetActive(self.ui.m_btnBack, true)
    else
        SetActive(self.ui.m_btnBack, false)
    end
end

function UI_CollectCardDraw:SetDefaultCardItem(index, isGold)
    local cardItem = self.ui.m_transCard:GetChild(index - 1);
    DOKill(cardItem.transform);

    local cardAnim = GetChild(cardItem, "item", UE.Animation)
    local animName = isGold and "drawcardDefault" or "drawcardDefault_1"
    PlayAnimStatus(cardAnim, animName)

    local child = self.ui.m_transCardPos:GetChild(index - 1)
    local pos = UIRectPosFit(child)
    cardItem.transform.localScale = Vector3(0.8, 0.8, 1);
    SetUIPos(cardItem, pos[1], pos[2]);
end

function UI_CollectCardDraw:SkipCardAnim()
    SetActive(self.ui.m_goPack, false);

    local count = self.cardIndex - 1;
    if count > 0 then
        for i = 1, count do
            local isGold = NetCollectCardData:IsGoldCard(v2n(self.rewardList[i]))
            self:SetDefaultCardItem(i, isGold);
        end
    end
    self.cardShowIndex = self.cardIndex;

    local count = self.cardMaxIndex - count;
    if count > 0 then
        for i = 1, count do
            self:CreateCardItem();
        end
    end
end

function UI_CollectCardDraw:OnDelayClose()
    if self.isPlayAni then
        TimeMgr:CreateTimer(UIDefine.UI_CollectCardDraw, function()
            self.isPlayAni = false;
        end, 0.8, 1);
    end
end

function UI_CollectCardDraw:OnPlayCloseAni()
    if not self.isPlayCloseAni then
        self.isPlayCloseAni = true;

        local canvasGroup = GetComponent(self.ui.m_goCollect, UE.CanvasGroup);
        canvasGroup.alpha = 1;

        -- self.ui.m_transCard:SetParent(self.ui.m_goCollect.transform);
        self.ui.m_doCollect:DORestart();

        -- DOScale(self.ui.m_transCard, Vector3(0, 0, 0), 0.8);
        -- DOLocalMove(self.ui.m_transCard, Vector3(0, 0, 0), 0.6, function()
        --     DOFadeAlpha(canvasGroup, 1, 0, 0.2, 0.2, 0, Ease.Linear, function()
        --         self:Close();
        --     end);
        -- end);

        local childCount = self.ui.m_transCard.childCount;
        -- local endPos = self.ui.m_transCard.localPosition;

        local endPos = UIRectPosFit(self.ui.m_goCollect)
        local item;
        local time = 0;
        for i = 1, childCount do
            item = self.ui.m_transCard:GetChild(i - 1);
            if time < 0.8 then
                time = time + 0.15;
            end
            DOScale(item, Vector3(0, 0, 0), time + 0.5);
            DOLocalMove(item, Vector3(endPos[1], endPos[2], 0), time);
        end

        TimeMgr:CreateTimer(UIDefine.UI_CollectCardDraw, function()
            DOFadeAlpha(canvasGroup, 1, 0, 0.3, 0.2, 0, Ease.Linear, function()
                self:Close();
            end);
        end, time + 0.2, 1);
    end
end

return UI_CollectCardDraw