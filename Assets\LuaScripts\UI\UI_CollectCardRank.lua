local UI_CollectCardRank = Class(BaseView)
local SlideRect = require("UI.Common.SlideRect")
local ItemBase = require("UI.Common.BaseSlideItem")
local RankItem = Class(ItemBase)
local ItemShowNums = 8

local RankType = {
    All = 1,     -- 全服
    Area = 2,    -- 区域
    League = 3,  -- 联盟
    Friend = 4,  -- 好友
}

local ContentType = {
    Collection = 100,  -- 收藏册
    CardStar = 200,    -- 星级
}

local curContent = ContentType.Collection;

function UI_CollectCardRank:OnInit()
    EventMgr:Add(EventID.UNION_ID_CHANGE, self.CheckLeagueInfo, self);
    EventMgr:Add(EventID.AGREE_FRIEND, self.CheckFriendInfo, self);
    EventMgr:Add(EventID.DELETE_FRIEND, self.CheckFriendInfo, self);
end

function UI_CollectCardRank:OnCreate(param)
    local otherHeadNode = GetChild(self.ui.m_goItemRank, "headNode",UE.Transform)
    CreateCommonHeadAsync(otherHeadNode, 0.5)
    local myHeadNode = GetChild(self.ui.m_goMyselfRank, "headNode",UE.Transform)
    CreateCommonHeadAsync(myHeadNode, 0.5, nil, function (go, trans)
        self.myHeadNode = go
    end)

    self.btn_unionrank_light = GetChild(self.ui.m_btnUnionRank, "btn_unionrank_light")
    self.btn_arearank_light = GetChild(self.ui.m_btnAreaRank, "btn_arearank_light")
    self.btn_fubenrank_light = GetChild(self.ui.m_btnFuBenRank, "btn_fubenrank_light")
    self.btn_zoorank_light = GetChild(self.ui.m_btnZooRank, "btn_zoorank_light")

    self.btn_myunion_light = GetChild(self.ui.m_btnTabMyUnion,"btn_myunion_light")
    self.btn_cheat_light = GetChild(self.ui.m_btnTabChat,"btn_cheat_light")

    self.type = RankType.All
    self.content = ContentType.Collection

    local leagueId = LeagueManager:GetMyLeagueId()
    self.isJoinTeam = leagueId and leagueId > 0

    self.canClickFriend = true
    NetFriendData:RequestFriendList(FRIEND_REQUEST_TYPE.Friend, function (data)
        self.friendList = data
        self.isHasFriend = self.friendList and table.count(self.friendList) > 0
    end)

    self:InitPanel()
end

function UI_CollectCardRank:OnRefresh(param)

end

function UI_CollectCardRank:onDestroy()
    EventMgr:Remove(EventID.UNION_ID_CHANGE, self.CheckLeagueInfo, self);
    EventMgr:Remove(EventID.AGREE_FRIEND, self.CheckFriendInfo, self);
    EventMgr:Remove(EventID.DELETE_FRIEND, self.CheckFriendInfo, self);

    Tween.Kill("AutoMoveFunc")
    if self.playerList then
        for i = 1, #self.playerList do
            self.playerList[i]:onDestroy()
        end
    end
    self.playerList = nil
    self.rankSlider = nil
    self.myHeadNode = nil
end

function UI_CollectCardRank:onUIEventClick(go,param)
    local name = go.name
    -- 关闭按钮
    if name == "m_btnClose" then
        self:Close()
    elseif name == "m_btnFuBenRank" then
        self:OnClickTab(RankType.All)
    elseif name == "m_btnAreaRank" then
        self:OnClickTab(RankType.Area)
    elseif name == "m_btnUnionRank" then
        self:OnClickTab(RankType.League)
    elseif name == "m_btnZooRank" then
        if self.canClickFriend then
            self:OnClickTab(RankType.Friend)
        end
    elseif name == "m_btnTabChat" then
        self:OnClickTab(nil, ContentType.Collection)
    elseif name == "m_btnTabMyUnion" then
        self:OnClickTab(nil, ContentType.CardStar)
    elseif name == "m_btnAdd" then
        if self.type == RankType.League then
            if not LeagueManager:IsOpenUnion() then
                local str = LangMgr:GetLangFormat(7076, LeagueManager:GetConfigById(2))
                UI_SHOW(UIDefine.UI_WidgetTip, str)
                return
            end
            UI_SHOW(UIDefine.UI_Union, 2)
        elseif self.type == RankType.Friend then
            if not FriendManager:IsOpen() then
                local str = LangMgr:GetLangFormat(7076, FriendManager:GetConfigById(101))
                UI_SHOW(UIDefine.UI_WidgetTip, str)
                return
            end
            UI_SHOW(UIDefine.UI_FriendAdd)
        end
    end
end

function UI_CollectCardRank:CheckLeagueInfo()
    local leagueId = LeagueManager:GetMyLeagueId()
    self.isJoinTeam = leagueId and leagueId > 0
    if self.type == RankType.League then
        self:OnClickTab(RankType.League)
    end
end

function UI_CollectCardRank:CheckFriendInfo()
    self.friendList = NetFriendData.friendList
    self.isHasFriend = self.friendList and table.count(self.friendList) > 0;
    if self.type == RankType.Friend then
        self:OnClickTab(RankType.Friend)
    end
end

--- 初始化界面
function UI_CollectCardRank:InitPanel()
    self:InitRank()
    self:OnClickTab(RankType.All, nil, true)
    self:OnClickTab(nil, ContentType.Collection)
end

--- 初始化排行榜
function UI_CollectCardRank:InitRank()
    self.rankSlider = SlideRect.new()
    self.rankSlider:Init(self.uiGameObject.transform:Find("state/m_goRankScroll/ViewPort"):GetComponent(typeof(UEUI.ScrollRect)),2)
    self.playerList = {}
    for i = 1, ItemShowNums do
        self.playerList[i] = RankItem.new()
        self.playerList[i]:Init(UEGO.Instantiate(self.ui.m_goItemRank.transform))
    end
    self.rankSlider:SetItems(self.playerList, 12, Vector2.New(4, 0))
end

--- 刷新排行榜
--- @param type number 类型
function UI_CollectCardRank:RefreshRank(type)
    local function rankCallBack(data)
        self.playerListData = data.ranking
        self.myselfData = data.player
        if self.playerListData then
            self:UpdateRankData(self.playerListData, 1)
        else
            self:UpdateRankData({}, 1)
        end
        if self.myselfData then
            self:SetMyself()
            SetActive(self.ui.m_goMyselfRank, true);
        else
            SetActive(self.ui.m_goMyselfRank, false);
        end
    end
    NetCollectCardData:RequestRankData(rankCallBack, type)
end

function UI_CollectCardRank:OnClickTab(type, content, noRequest)

    SetActive(self.ui.m_goTip, false)

    if type then
        self.type = type
        SetActive(self.btn_fubenrank_light, type == RankType.All)
        SetActive(self.btn_arearank_light, type == RankType.Area)
        SetActive(self.btn_zoorank_light, type == RankType.Friend)
        SetActive(self.btn_unionrank_light, type == RankType.League)

        SetUISize(self.ui.m_btnFuBenRank, type == RankType.All and 255 or 232, 90)
        SetUISize(self.ui.m_btnAreaRank, type == RankType.Area and 255 or 232, 90)
        SetUISize(self.ui.m_btnUnionRank, type == RankType.League and 255 or 232, 90)
        SetUISize(self.ui.m_btnZooRank, type == RankType.Friend and 255 or 232, 90)
    end

    if content then
        self.content = content
        curContent = content;
        SetActive(self.btn_myunion_light, content == ContentType.CardStar)
        SetActive(self.btn_cheat_light, content == ContentType.Collection)

        SetUISize(self.ui.m_btnTabMyUnion, content == ContentType.CardStar and 286 or 246, 171)
        SetUISize(self.ui.m_btnTabChat, content == ContentType.Collection and 286 or 246, 171)
    end

    if self.type == RankType.League and not self.isJoinTeam then
        SetActive(self.ui.m_goTip, true)
        self.ui.m_txtTip.text = LangMgr:GetLang(9055)
        self.ui.m_txtAdd.text = LangMgr:GetLang(9207)
        SetActive(self.ui.m_btnAdd, true)
        noRequest = true
        self:UpdateRankData({}, 1, noRequest)
        SetActive(self.ui.m_goMyselfRank, false);
    elseif self.type == RankType.Friend and not self.isHasFriend then
        SetActive(self.ui.m_goTip, true)
        self.ui.m_txtTip.text = LangMgr:GetLang(9043)
        self.ui.m_txtAdd.text = LangMgr:GetLang(9014)
        SetActive(self.ui.m_btnAdd, true)
        noRequest = true
        self:UpdateRankData({}, 1, noRequest)
        SetActive(self.ui.m_goMyselfRank, false);
    end

    if noRequest then return end
    local requestType = self.type + self.content
    self:RefreshRank(requestType)
end

function UI_CollectCardRank:UpdateRankData(list, index, noRequest)
    self.rankSlider:SetData(list, index);

    if not noRequest and #list <= 0 then
        self.ui.m_txtTip.text = LangMgr:GetLang(51029118);
        SetActive(self.ui.m_goTip, true);
        SetActive(self.ui.m_btnAdd, false);
    end
end

--- 设置自己的排名
function UI_CollectCardRank:SetMyself()
    if not self.ui then return end
    local rank = v2n(self.myselfData.rank)
    if not rank then return end
    if self.isPush then
        local ranking = v2n(self.myselfData.ranking)
        if ranking and ranking > 0 then
            rank = ranking
        end
    end
    local transform = self.ui.m_goMyselfRank
    self.txt_level  = GetChild(transform, "border/txt_level", UEUI.Text)
    self.txt_rank   = GetChild(transform, "rank/txt_rank", UEUI.Text)
    self.txt_name   = GetChild(transform, "txt_name", UEUI.Text)
    --self.img_head   = GetChild(transform, "head/img_head", UEUI.Image)
    self.img_rank   = GetChild(transform, "rank/img_rank", UEUI.Image)
    self.rankStage  = GetChild(transform, "img_rank", UEUI.Image)
    self.txt_lang   = GetChild(transform, "txt_lang", UEUI.Text)
    self.scoreIcon  = GetChild(transform, "border/Image", UEUI.Image)

    -- 前三名的排名用图标显示
    if rank and rank <= 3 then
        local rankSprite
        if rank == 1 then
            rankSprite = "Sprite/ui_huodongjingsai_paihangbang/paihang_win4_icon1.png"
        elseif rank == 2 then
            rankSprite = "Sprite/ui_huodongjingsai_paihangbang/paihang_win4_icon2.png"
        elseif rank == 3 then
            rankSprite = "Sprite/ui_huodongjingsai_paihangbang/paihang_win4_icon3.png"
        end
        if rankSprite then
            SetUIImage(self.img_rank, rankSprite, false)
            SetActive(self.img_rank, true)
            SetActive(self.txt_rank, false)
        end
    else
        SetActive(self.img_rank, false)
        SetActive(self.txt_rank, true)
    end
    -- 名字
    self.txt_name.text = NetUpdatePlayerData:GetPlayerInfo().name
    -- 头像
    --local playerHead = NetUpdatePlayerData:GetPlayerInfo().head
    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, playerHead)
    --if headConfig then
    --    SetUIImage(self.img_head, headConfig.icon, false)
    --end
    SetMyHeadAndBorderByGo(self.myHeadNode)

    -- 排名
    if rank == 0 then
        self.txt_rank.text = LangMgr:GetLang(9056);
    else
        self.txt_rank.text = rank
    end
    -- 分数
    self.txt_level.text = self.myselfData.point

    local rankIcon = FriendManager:GetRankStageIcon(self.myselfData.stage)
    SetImageSprite(self.rankStage, rankIcon, false)

    self.txt_lang.text = FriendManager:GetLanguage(self.myselfData)

    if self.content == ContentType.Collection then
        SetImageSprite(self.scoreIcon, "Sprite/ui_public/Resource_jifen_kapai.png", false);
    else
        SetImageSprite(self.scoreIcon, "Sprite/ui_tujian/jika_xiangqing_star.png", false);
    end
end

function RankItem:OnInit(transform)
    self.trans      = transform
    self.bg         = GetChild(transform, "bg", UEUI.Image)
    self.txt_level  = GetChild(transform, "border/txt_level", UEUI.Text)
    self.txt_rank   = GetChild(transform, "rank/txt_rank", UEUI.Text)
    self.txt_name   = GetChild(transform, "txt_name", UEUI.Text)
    self.img_head   = GetChild(transform, "head/img_head", UEUI.Image)
    self.img_rank   = GetChild(transform, "rank/img_rank", UEUI.Image)
    self.rankStage  = GetChild(transform, "img_rank", UEUI.Image)
    self.txt_lang   = GetChild(transform, "txt_lang", UEUI.Text)
    self.scoreIcon  = GetChild(transform, "border/Image", UEUI.Image)
    self.customHeadObj      = GetChild(transform,"headNode/CustomHead")
    self.border     = GetChild(transform, "border", UEUI.Image)
end

function RankItem:UpdateData(data, index)
    if not data then return end
    -- 前三名的排名用图标显示
    if data.rank and data.rank <= 3 then
        local rankSprite
        if data.rank == 1 then
            rankSprite = "Sprite/ui_huodongjingsai_paihangbang/paihang_win4_icon1.png"
        elseif data.rank == 2 then
            rankSprite = "Sprite/ui_huodongjingsai_paihangbang/paihang_win4_icon2.png"
        elseif data.rank == 3 then
            rankSprite = "Sprite/ui_huodongjingsai_paihangbang/paihang_win4_icon3.png"
        end
        if rankSprite then
            SetUIImage(self.img_rank, rankSprite, false)
            SetActive(self.img_rank.gameObject, true)
        end
    else
        SetActive(self.img_rank.gameObject,false)
    end
    -- 名字
    self.txt_name.text = data.name
    -- 头像
    --local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, data.icon)
    --if headConfig then
    --    SetUIImage(self.img_head, headConfig.icon, false)
    --end

    SetHeadAndBorderByGo(self.customHeadObj,data.icon,data.border)
    self.customHeadObj.transform.localPosition = Vector3.zero

    -- 分数
    self.txt_level.text = data.point
    -- 自己
    if data.playerId then
        self.txt_rank.text = data.rank
        self.txt_rank.color = Color.HexToRGB("#245598")
        SetUIImage(self.bg, "Sprite/ui_paihangbang/paihangbang_dilang2.png", false)
        SetUIImage(self.border, "Sprite/ui_paihangbang/paihangbang_dilang3_1.png", false)
        UnifyOutline(self.txt_level, "#245598")
        UnifyOutline(self.txt_name, "#245598")
    -- 他人
    else
        self.txt_rank.text = data.rank
        self.txt_rank.color = Color.HexToRGB("#aa4f01")
        SetUIImage(self.bg, "Sprite/ui_paihangbang/paihangbang_dilang1.png", false)
        SetUIImage(self.border, "Sprite/ui_paihangbang/paihangbang_dilang1_1.png", false)
        UnifyOutline(self.txt_level, "#ae4a00")
        UnifyOutline(self.txt_name, "#95512a")
    end
    local rankIcon = FriendManager:GetRankStageIcon(data.stage)
    SetImageSprite(self.rankStage, rankIcon, false)

    self.txt_lang.text = FriendManager:GetLanguage(data)

    if curContent == ContentType.Collection then
        SetImageSprite(self.scoreIcon, "Sprite/ui_public/Resource_jifen_kapai.png", false);
    else
        SetImageSprite(self.scoreIcon, "Sprite/ui_tujian/jika_xiangqing_star.png", false);
    end
end

function RankItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function RankItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function RankItem:onDestroy()
    UEGO.Destroy(self.trans.gameObject)
    self.trans = nil
    self.txt_level = nil
end

return UI_CollectCardRank