local UI_CollectionNew = Class(BaseView)
local RankBox = require("UI.RankBox")

local TitleId = {
    [1] = {
        mainTitle = 2000001;
        rankTitle = 51029133;--"主岛屿排行"
    };
    [2] = {
        mainTitle = 2000002;
        rankTitle = 51029134;--"金谷农场排行"
    }
}

function UI_CollectionNew:OnInit()
    NetCollection:CheckCollectionRedDot()
end

function UI_CollectionNew:OnCreate(param,is_push)
    self.m_ListItemShow = {}
    self.tabID = 1
    if param then
        self.tabID = param
    end
    
    self.scrollRect = GetChild(self.uiGameObject,"goodObj/TableCtrl/m_TableView",UEUI.ScrollRect)
    self.contentRect = GetComponent(self.ui.m_goParent,UE.RectTransform)
    self:InitUI()

    self.rankBox = nil
    EventMgr:Add(EventID.GLOBAL_TOUCH_BEGIN, self.OnGlobalTouchBegin, self)
    
end

function UI_CollectionNew:InitUI()
    if self.tabID == nil then
        return
    end
    self.curMapID = NetUpdatePlayerData.playerInfo.curMap
    self:SetTitleView()
    self.itemList = {}  --缓存列表item
    self:DisplayGroupList()
    
    self:FocusRewardItem()
end

function UI_CollectionNew:OnRefresh(param)
    if param == NetCollection.UpdateType.ReceiveUnlockReward then
        self:DisplayGroupList()
    elseif param == NetCollection.UpdateType.ReceiveGroupReward then
        self:DisplayGroupList()
    elseif param == NetCollection.UpdateType.RefreshCollectScore then
        self.ui.m_txtScore.text = NetCollection:GetCollectionSocre(self.curMapID);
    end
end

function UI_CollectionNew:onDestroy()
    self.contentRect:DOKill();
    self.contentRect = nil
    EventMgr:Remove(EventID.GLOBAL_TOUCH_BEGIN, self.OnGlobalTouchBegin, self)
end

function UI_CollectionNew:onUIEventClick(go,param)
    local name = go.name
    if name == "m_btnRank" then
        --查看排行榜
        local index = 5
        if self.curMapID == 1 then
            index = 5
        elseif self.curMapID == 2 then
            index = 6
        end
        FriendManager:OnOpenRank(index);
    elseif name == "m_btnScore" then
        UI_SHOW(UIDefine.UI_TipsTop, 51029140);
    end
end

--********************************业务逻辑********************************
--显示标题的逻辑
function UI_CollectionNew:SetTitleView()
    local config = CollectionItems:GetCollectionNewConfigAllByTabIndex(self.tabID)
    --标题名
    self.ui.m_txtTitle.text = LangMgr:GetLang(TitleId[self.curMapID].mainTitle)
    --排行榜标题名
    self.ui.m_txtRankTitle.text = LangMgr:GetLang(TitleId[self.curMapID].rankTitle)
    --图鉴积分
    self.ui.m_txtScore.text = NetCollection:GetCollectionSocre(self.curMapID);
    if config and config[1] and config[1].percentage_bg then
        --标题图标
        SetUIImage(self.ui.m_imgTitleIcon,config[1].percentage_bg,false)
    end
end

--展示图鉴组列表
function UI_CollectionNew:DisplayGroupList()
    local config = CollectionItems:GetCollectionNewConfigAllByTabIndex(self.tabID)
    if next(self.itemList) == nil then
        local itemContent = self.ui.m_goParent
        local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "List_CollectCell_New")
        local function callBack(cellObj)
			for i, data in ipairs(config) do
				local itemGo = UEGO.Instantiate(cellObj)
				SetParent(itemGo, itemContent)
				itemGo.transform:SetLocalScale(1, 1, 1)
				SetActive(itemGo, true)
				self.itemList[i] = itemGo
				--self:UpdateCell(itemGo, data)
			end
			for i, data in ipairs(config) do
				local itemGo = self.itemList[i]
				self:UpdateCell(itemGo, data)
			end
		end
		ResMgr:LoadAssetAsync(assetPath, AssetDefine.LoadType.Instant,callBack)
	else
		for i, data in ipairs(config) do
			local itemGo = self.itemList[i]
			self:UpdateCell(itemGo, data)
		end
    end
end

function UI_CollectionNew:UpdateCell(itemGo,config)
    local icon = GetChild(itemGo,"bg1/Mask/itemCellIcon",UEUI.Image)
    local name = GetChild(itemGo,"bg1/nameBg/itemName",UEUI.Text)
    local progress = GetChild(itemGo,"bg1/SliderBg/m_sliderExp",UEUI.Slider)
    local txt_progress = GetChild(itemGo,"bg1/SliderBg/m_sliderExp/itemProgress",UEUI.Text)
    local btnGiftPackObj = GetChild(itemGo, "bg1/SliderBg/giftBox/giftPack", UEUI.Button)
    local giftPackPopPos = GetChild(itemGo, "bg1/SliderBg/giftPackPopPos")
    local btnCheck = GetChild(itemGo, "bg1", UEUI.Button)
    local sliderNode = GetChild(itemGo,"bg1/SliderBg")
    local lockMask = GetChild(itemGo,"lock")
    local rewardBox = GetChild(itemGo,"bg1/SliderBg/giftBox")
    local boxAnim = GetChild(itemGo,"bg1/SliderBg/giftBox", UE.Animation)
    local redDot = GetChild(itemGo,"bg1/redPoint")
    local boxEffect = GetChild(itemGo,"bg1/SliderBg/ImgEffect")
    local received = GetChild(itemGo,"bg1/received")
    local lockTip = GetChild(itemGo,"lock/lockTip",UEUI.Text)

    local groupId = config.id
    
    local usConfig = config.toggle_normal
    if usConfig then
        SetUIImage(icon, usConfig, true)
    end
    
    --该图集是否解锁
    local isUnlock = NetCollection:CheckGroupUnlock(groupId)
    SetActive(sliderNode,isUnlock)
    SetActive(lockMask,not isUnlock)
    SetActive(received,false)
    
    name.text = isUnlock and LangMgr:GetLang(config.name) or LangMgr:GetLang(7017)
    lockTip.text = LangMgr:GetLang(93)
    SetActive(lockTip,not isUnlock)
    if not isUnlock then
        SetActive(redDot,false)
        return
    end
    local ratio,value1,value2 = self:CheckProgress(groupId)
    txt_progress.text = value1.."/"..value2
    progress.value = ratio
    --该组图鉴是否已收集完成
    local isFinish = (value1 == value2)
    --奖励是否已领取
    local hasReceived = false
    boxAnim:Stop()
    if isFinish then
        if groupId == 1 or groupId == 101 then
            hasReceived = NetCollection:CheckGroupReceive(1) or NetCollection:CheckGroupReceive(101)
        else
            hasReceived = NetCollection:CheckGroupReceive(groupId)
        end
        
        if hasReceived then
            boxAnim:Stop()
        else
            boxAnim:Play("animation_giftpack")
        end
    else
        boxAnim:Stop()
    end
    local showRedDot = self:CheckGroupRedDot(groupId)
    SetActive(redDot,showRedDot)
    SetActive(boxEffect,isFinish and not hasReceived)
    SetActive(sliderNode,not hasReceived)
    SetActive(received,hasReceived)
    
    RemoveUIComponentEventCallback(btnCheck, UEUI.Button)
    AddUIComponentEventCallback(btnCheck, UEUI.Button, function()
        UI_SHOW(UIDefine.UI_CollectionDetail,config)
    end)
    
    RemoveUIComponentEventCallback(btnGiftPackObj, UEUI.Button)
    AddUIComponentEventCallback(btnGiftPackObj, UEUI.Button, function()
        local isReceived = NetCollection:CheckGroupReceive(groupId)
        local canReceive = isFinish and not isReceived
        if canReceive then
            NetCollection:GetGroupReward(groupId)
            UI_UPDATE(UIDefine.UI_CollectionCenter,"RefreshRedDot",1)
            local flyPos = UIMgr:GetObjectScreenPos(rewardBox.transform)
            NetGlobalData:GetRewardToMap(config.reward,"UI_Collection",nil,nil,flyPos,true)
        else
            self:CreateRankBox(giftPackPopPos,config.reward)
        end
    end)
end

--获取图鉴收集进度
function UI_CollectionNew:CheckProgress(id)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.collection_new,id)
    local sumCount = 0
    local unlockCount = 0
    for k,v in ipairs(config.collection_ids) do
        local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.collection_items,v)
        if itemConfig then
            local isRes = (itemConfig.is_res and itemConfig.is_res == 1)
            for m,n in ipairs(itemConfig.materialList) do
                sumCount = sumCount + 1
                local hasOwn = CollectionItems:CheckResTypeUnlock(isRes,n)
                if hasOwn then
                    unlockCount = unlockCount + 1
                end
            end
        end
    end
    unlockCount = unlockCount or 0
    sumCount = (sumCount and sumCount > 0) and sumCount or 1
    local ratio = unlockCount/sumCount
    ratio = (ratio > 1) and 1 or ratio
    return ratio,unlockCount,sumCount
end

--判断图鉴集红点逻辑
function UI_CollectionNew:CheckGroupRedDot(id)
    local result = NetCollection:CheckEveryItem(id,function(itemId,isRes)
        local hasOwn = CollectionItems:CheckResTypeUnlock(isRes,itemId)
        if hasOwn then
            local matState = NetCollection:get_ItemState(itemId)
            if not matState then
                return true
            end
        end
        return false
    end)
    return result
end

--聚焦定位到可领奖的item的位置
function UI_CollectionNew:FocusRewardItem()
    local targetRowIndex = self:GetFocusRow()
    self:ScrollToTarget(targetRowIndex)
end

--获取需要聚焦的行数
function UI_CollectionNew:GetFocusRow()
    local config = CollectionItems:GetCollectionNewConfigAllByTabIndex(self.tabID)
    for k,v in ipairs(config) do
        local groupId = k
        if NetCollection:CheckTargetGroupReward(groupId) then
            return math.ceil(groupId/3)
        end
    end
    return 0
end

--（动画表现）滑动到可领奖位置
function UI_CollectionNew:ScrollToTarget(rowIndex)
    SetUIForceRebuildLayout(self.contentRect)
    rowIndex = (rowIndex <= 0) and 1 or rowIndex
    local config = CollectionItems:GetCollectionNewConfigAllByTabIndex(self.tabID)
    local maxRow = math.ceil(#config/3)
    if rowIndex >= maxRow then
        self.scrollRect.normalizedPosition = Vector2.New(0,0);
    else
        local padding = 30*(rowIndex-1)
        padding = (padding > 0) and padding or 0
        local moveValue = (rowIndex-1)*350+padding
        self.contentRect:DOAnchorPos(Vector2.New(0, moveValue), rowIndex*0.1);
    end
end

function UI_CollectionNew:OnGlobalTouchBegin(vtArray,touchCount,isForce3D)
    self:RemoveRankBox()
end

function UI_CollectionNew:CreateRankBox(obj,rewards)
    if self.rankBox == nil then
        self.rankBox = RankBox.new(self.uiGameObject)
        self.rankBox:InitUI(3,nil,function()
            self.rankBox:UpdateItem(obj,rewards)
        end)
    end
end

function UI_CollectionNew:RemoveRankBox()
    if self.rankBox then
        self.rankBox:Destory()
        self.rankBox = nil
    end
end

return UI_CollectionNew