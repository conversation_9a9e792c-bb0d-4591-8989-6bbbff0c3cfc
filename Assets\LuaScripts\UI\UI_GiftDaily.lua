local UI_GiftDaily = Class(BaseView)
local ItemBase = require("UI.Common.BaseSlideItem")
local GiftItem = Class(ItemBase)
local ItemShowNums = 7

local Vector3One = Vector3.New(1,1,1)
local Vector3Zero = Vector3.New(0,0,0)

--挡位显示配置
local LevelConfig = {
	[1] = {
		titleHex = "0C5CE1";--标题描边颜色
		rewardHex = "3D6B86";--奖励描边颜色
		limitHex = "1A499F";--限购文本颜色
	};
	[2] = {
		titleHex = "B001A6";
		rewardHex = "5f1b99";
		limitHex = "58227F";
	};
	[3] = {
		titleHex = "D72F43";
		rewardHex = "AB4800";
		limitHex = "9C0E3B";
	};
}

function UI_GiftDaily:OnInit()
    
end

function UI_GiftDaily:OnCreate(param)
	self.userType = 1
	if NetGrowthFundNew:IsOpenActivity() then
		--新用户
		self.userType = 2
	end
   	self.clickType = DAILY_GIFT.DAY
	local config = ConfigMgr:GetData(ConfigDefine.ID.gift_daily)
	self.config = {}
	for k, v in pairs(config) do
		if v.is_new == self.userType then
			table.insert(self.config,v)
		end
	end
	self.dayConfig = {}
	self.weekConfig = {}
	self.MonthConfig = {}
	self.togglist = {}
	self:SetIsUpdateTick(true)
	for k, v in pairs(self.config) do
		if v.group == DAILY_GIFT.DAY then
			if v.map_id and NetGiftDaily:GiftIsCanShowByMap(v.map_id,v.level) then
				table.insert(self.dayConfig,v)
				self:AddToggleByType(DAILY_GIFT.DAY)
			end
		elseif v.group == DAILY_GIFT.WEEK then
			if v.map_id and NetGiftDaily:GiftIsCanShowByMap(v.map_id,v.level) then
				table.insert(self.weekConfig,v)
				self:AddToggleByType(DAILY_GIFT.WEEK)
			end
		elseif v.group == DAILY_GIFT.MONTH then
			if v.map_id and NetGiftDaily:GiftIsCanShowByMap(v.map_id,v.level) then
				table.insert(self.MonthConfig,v)
				self:AddToggleByType(DAILY_GIFT.MONTH)
			end
		end
	end

	self.content = GetChild(self.uiGameObject,"bg/m_goGiftviewPort/content",UE.RectTransform)
	self.slider = require("UI.Common.SlideRect").new()
	self.slider:Init(self.uiGameObject.transform:Find("bg/m_goGiftviewPort"):GetComponent(typeof(UEUI.ScrollRect)),2)
	self.giftItemList = {}
	for i = 1, ItemShowNums do
		self.giftItemList[i] = GiftItem.new()
		self.giftItemList[i]:Init(UEGO.Instantiate(self.ui.m_goGiftCell.transform))
	end
	self.slider:SetItems(self.giftItemList,0,Vector2.New(0,0))
	
	SetActive(self.ui.m_goTog,false)

	self:SetRewardBoxView(not NetGiftDaily.data.getFreeDay)
	self:OnClickTabEnd(self.clickType)

	SetActive(self.ui.m_btnBack,false)
	
	--self:SetCustomCloseFun(false)
	--DOLocalMoveY(self.ui.m_goTop.transform,-117,0.3,nil,Ease.OutQuint)
	--DOLocalMoveX(self.ui.m_goGiftviewPort.transform,207,0.3,nil,Ease.OutQuint)
end

function UI_GiftDaily:onCustomCloseFunPlay(playEnd)
	EventMgr:Dispatch(EventID.ACT_ANIM_BEGIN)
	self.customCloseFun = false
	DOLocalMoveY(self.ui.m_goTop.transform,365,0.3,nil,Ease.OutQuint)
	DOLocalMoveX(self.ui.m_goGiftviewPort.transform,1800,0.3,function ()
			EventMgr:Dispatch(EventID.ACT_ANIM_DONE)
			playEnd()
	end,Ease.OutQuint)
end

function UI_GiftDaily:AddToggleByType(type)
	local key = tostring(type)
	if self.togglist[type] == nil then
		local go = UEGO.Instantiate(self.ui.m_goTog.transform)
		go.transform:SetParent(self.ui.m_goParentTog.transform)
		go.transform.localScale = Vector3.New(1, 1, 1)
		go.transform:SetLocalPosition(0, 0, 0)
		SetActive(go, true)
		local toggle = go:GetComponent(typeof(UEUI.Toggle))
		toggle.onValueChanged:AddListener(function (isOn)
			if isOn then
				self:OnClickTabEnd(type)
			end
		end)
		self:SetToggleView(go,type)
		self.togglist[type] = go
	end
end

function UI_GiftDaily:SetToggleView(togObj,type)
	local rect = GetComponent(togObj, UE.RectTransform)
	local unselectBg = GetChild(togObj, "Background", UEUI.Image)
	local selectBg = GetChild(togObj, "Background/Checkmark", UEUI.Image)

	local togName = GetChild(togObj, "togName", UEUI.Text)
	local togNameLight = GetChild(togObj, "Background/Checkmark/togName_light", UEUI.Text)
	
	local TypeConfig = {
		[DAILY_GIFT.DAY] = {
			titleName = 2201001;
			width = 333;
			height = 92;
			unselectBg = "rika_dikuang1";
			selectBg = "rika_dikuang1-1";
		};
		[DAILY_GIFT.WEEK] = {
			titleName = 2201002;
			width = 327;
			height = 95;
			unselectBg = "rika_dikuang2";
			selectBg = "rika_dikuang2-1";
		};
		[DAILY_GIFT.MONTH] = {
			titleName = 2201003;
			width = 333;
			height = 92;
			unselectBg = "rika_dikuang3";
			selectBg = "rika_dikuang3-1";
		};
	}
	
	rect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Horizontal,TypeConfig[type].width);
	rect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Vertical,TypeConfig[type].height);

	self:SetSprite(unselectBg,TypeConfig[type].unselectBg)
	self:SetSprite(selectBg,TypeConfig[type].selectBg)
	
	local name = LangMgr:GetLang(TypeConfig[type].titleName)
	togName.text = name
	togNameLight.text = name
end

function UI_GiftDaily:SetSprite(img, spriteName)
	local path = "Sprite/ui_huodongzhongxin/"..spriteName..".png"
	SetUIImage(img,path,false)
end

function UI_GiftDaily:SetItemListDatas(datas)
	--if not datas then
		--return
	--end
	--local noSlodOut = {}
	--local slodOut = {}
	--for k, v in pairs(datas) do
		--local buyTimes = NetGiftDaily:GetTimesByTypeAndPayId(v.group,v.pay_id)
		--local maxTimes = v.max
		--if buyTimes >= maxTimes then
			--table.insert(slodOut,v)
		--else
			--table.insert(noSlodOut,v)
		--end
	--end
	--table.sort(slodOut, function(a, b)
			--return a.sort < b.sort
		--end)
	--table.sort(noSlodOut, function(a, b)
			--return a.sort < b.sort
		--end)
	--table.insertto(noSlodOut,slodOut)
	self.slider:SetData(datas)
	
	UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.content);
	
end

function UI_GiftDaily:OnClickTabEnd(type)
	self.clickType = type
	if self.clickType == DAILY_GIFT.DAY then
		self:SetItemListDatas(self.dayConfig)
	elseif self.clickType == DAILY_GIFT.WEEK then
		self:SetItemListDatas(self.weekConfig)
	elseif self.clickType == DAILY_GIFT.MONTH then
		self:SetItemListDatas(self.MonthConfig)
	end
end

function UI_GiftDaily:OnRefresh(type,_param)
    if type == 1 then
		--pay
		local config = ConfigMgr:GetDataByKey(ConfigDefine.ID.gift_daily,"pay_id",_param)
		if config then
			local param = {}
			param.type = 13
			param.rewards = config.reward
			UI_SHOW(UIDefine.UI_Recharge, param, nil, false)
		end
		
		self:OnClickTabEnd(self.clickType)
	elseif type == 2 then
		--freereward
		local param = {}
		param.type = 13
		param.rewards = _param
		UI_SHOW(UIDefine.UI_Recharge, param, nil, false)
		self:SetRewardBoxView(false)
		
		UI_UPDATE(UIMgr:GetNowMainUI(),25)
	end
	
end

function UI_GiftDaily:TickUI(delta)
	local time = NetGiftDaily:GetEndTimeByType(self.clickType)
	local freeDayRestTime = NetGiftDaily:GetFreeDayRestTime()
	local nowTime = TimeMgr:GetServerTimestamp()
	if time-nowTime > 0 then
		self.ui.m_txtTime.text = TimeMgr:CutBuyWorkTime(time-nowTime)
	else
		self.ui.m_txtTime.text = ""
	end
	if freeDayRestTime - nowTime < 0 then
		NetGiftDaily:ResetFreeDayState()
		self:SetRewardBoxView(not NetGiftDaily.data.getFreeDay)
	end
end

function UI_GiftDaily:onUIEventClick(go,param)
    local name = go.name
	if name == "btn_close" then
		self:Close()
	elseif name == "m_btnFreeReward" then
		NetGiftDaily:GetFreeReward()
	elseif name == "m_btnCheckReward" then
		self:CheckBoxReward()
	elseif name == "m_btnBack" then
		SetActive(self.ui.m_btnBack,false)
		self.ui.m_goBoxCheck.transform:DOScale(Vector3Zero,0.3)
	end
end

function UI_GiftDaily:onDestroy()
	self.slider = nil
	DOKill(self.ui.m_goTop.transform)
	DOKill(self.ui.m_goGiftviewPort.transform)
end

function GiftItem:OnInit(transform)
	local obj = transform
	self.AnimObj          = GetChild(obj, "AnimObj", UE.RectTransform)
	self.bgImg            = GetChild(obj, "AnimObj/bgImg", UEUI.Image)
	self.BtnCliam         = GetChild(obj, "AnimObj/m_BtnCliam", UEUI.Button)
	self.ItemImgBg        = GetChild(obj, "AnimObj/bgImg/ItemIng", UEUI.Image)
	self.name             = GetChild(obj, "AnimObj/m_txtName", UEUI.Text)
	self.tipBtn           = GetChild(obj, "AnimObj/m_btnTip", UEUI.Button)
	self.markImg          = GetChild(obj, "AnimObj/bgImg/daizi", UEUI.Image)
	self.m_txtMoney 	  = GetChild(obj, "AnimObj/m_BtnCliam/deepList/m_txtMoney", UEUI.Text)
	self.txtXiangou 	  = GetChild(obj, "txtXiangou", UEUI.Text)
	
	self.flagObj          = GetChild(obj, "AnimObj/bgImg/flag")

	self.tehuiObj         = GetChild(obj, "image")
	self.txtTehui 		  = GetChild(obj, "image/tehui", UEUI.Text)
	self.goComplete       = GetChild(obj, "AnimObj/m_goComplete")
	self.soldOutMask = GetChild(obj, "mask")
	self.titleObj = GetChild(obj, "AnimObj/m_txtName")
	
	self.rewardCellList = {}
	local rewardList = GetChild(obj, "rewardList", UE.RectTransform)
	for i = 1, 6 do
		local cell = {}
		local obj = GetChild(rewardList, "itemReward"..i, UE.RectTransform)
		--local img = GetChild(obj.transform,name.."/iconImg",UEUI.Image)
		--local stone = GetComponent(self.ui.m_goRankBox, typeof(UEUI.Image))
		cell["obj"] = obj
		table.insert(self.rewardCellList,cell)
	end
end

function GiftItem:UpdateData(data,index)
	if nil == data then return end
	
	local levelType = data.sort
	UnifyOutline(self.titleObj,LevelConfig[levelType].titleHex)
	
	SetUIImage(self.bgImg,data.pic1,false)
	
	SetUIImage(self.markImg,data.pic2,false)
	
	--self.ItemImgBg.transform.localScale = Vector3.New(data.size, data.size, data.size)
	SetUIImage(self.ItemImgBg,data.icon,false)
	self.name.text = LangMgr:GetLang(data.langid1)

	local buyTimes = NetGiftDaily:GetTimesByTypeAndPayId(data.group,data.pay_id)
	local maxTimes = data.max
	self.txtXiangou.text = LangMgr:GetLangFormat(2201018, buyTimes, maxTimes)
	self.txtXiangou.color = self:GetColorByHex(LevelConfig[levelType].limitHex)
	
	local isSoldOut = (buyTimes >= maxTimes)
	--SetActive(self.goComplete,false)
	SetActive(self.BtnCliam,true)
	SetActive(self.soldOutMask,false)
	SetActive(self.tehuiObj,not isSoldOut)
	SetActive(self.flagObj,not isSoldOut)
	

	if not isSoldOut then
		local payConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.payment,data.pay_id)
		self.m_txtMoney.text = LangMgr:GetLang(payConfig.price_langid)
		self.txtTehui.text = LangMgr:GetLang(data.langid)
	else
		self.m_txtMoney.text = LangMgr:GetLang(53)
		
	end
	PurchaseManager:CreateScoreTag(data.pay_id,self.BtnCliam.transform,isSoldOut,Vector2.New(18,20))
	SetUIBtnGrayAndEnable(self.BtnCliam,not isSoldOut,"247700")
	

	self.txtTehui.text = LangMgr:GetLang(data.langid)
	local rewardT = string.split(data.reward,";")
	local index = table.count(rewardT)
	
	for k, v in pairs(self.rewardCellList) do
		if index >= k then
			local config = rewardT[k]
			local arr = string.split(config,"|")
			local itemId = v2n(NetSeasonActivity:GetChangeItemId(arr[1]))
			local num = v2n(arr[2])
			SetActive(v.obj,true)
			
			local itemBg = v.obj:GetComponent(typeof(UEUI.Image))
			local img = GetChild(v.obj,"icon",UEUI.Image)
			local _num = GetChild(v.obj,"rewardnum",UEUI.Text)	
			SetUIImage(itemBg,data.pic3,false)		
			SetUIImage(img, ItemConfig:GetIcon(itemId), false)
			_num.text = "x"..NumToGameString(num) 
			
			UnifyOutline(GetChild(v.obj,"rewardnum"),LevelConfig[levelType].rewardHex)
			
			local btn = v.obj:GetComponent(typeof(UEUI.Button))
			RemoveUIComponentEventCallback(btn,UEUI.Button)
			AddUIComponentEventCallback(btn, UEUI.Button, function(arg1,arg2)
					if itemId > 0  then
						UI_SHOW(UIDefine.UI_ItemTips,itemId)
					end
				end)
		else
			SetActive(v.obj,false)
		end
	end
	
	RemoveUIComponentEventCallback(self.BtnCliam)
	AddUIComponentEventCallback(self.BtnCliam, UEUI.Button, function(arg1,arg2)
			self:ClickBtn(data)
		end)
end

function GiftItem:ClickBtn(data)
	local buyTimes = NetGiftDaily:GetTimesByTypeAndPayId(data.group,data.pay_id)
	local maxTimes = data.max
	if buyTimes >= maxTimes then
		UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(2201019))
		return
	end
	PaymentConfig:ShowPay(data.pay_id)

end


function GiftItem:UpdatePosition(vec)
	self.rectTrans.anchoredPosition = vec
end

function GiftItem:GetAnchoredPositon()
	return self.rectTrans.anchoredPosition
end

function UI_GiftDaily:SetRewardBoxView(canReceive)
	SetActive(self.ui.m_btnFreeReward,canReceive)
	SetActive(self.ui.m_btnCheckReward,not canReceive)
	SetActive(self.ui.m_goReceived,not canReceive)
	SetActive(self.ui.m_goRewardRedDot,canReceive)

	local anim = self.ui.m_imgFreeIcon:GetComponent(typeof(UE.Animation))
    anim.enabled = canReceive;
end	

function UI_GiftDaily:CheckBoxReward()
	local trans = self.ui.m_goBoxCheck.transform
	self.ui.m_goBoxCheck.transform.localScale = Vector3.New(1,0,1)
	SetActive(self.ui.m_goBoxCheck,true)
	
	local rewardList = self:GetRewardList(ConfigDefine.ID.global_setting, 10602)
	local rewardNode = GetChild(self.ui.m_goBoxCheck,"bg/RewardNode")
	local itemPrefab = GetChild(rewardNode,"ItemBg")
	for i = 1, rewardNode.transform.childCount - 1, 1 do
		UEGO.Destroy(rewardNode.transform:GetChild(i).gameObject)
	end
	for index, reward in ipairs(rewardList) do
		local item = UEGO.Instantiate(itemPrefab,itemPrefab.transform.parent)
		item:SetActive(true)
		local itemImage = GetChild(item,"ItemIcon",UEUI.Image)
		local itemText = item:GetComponentInChildren(typeof(UEUI.Text))
		SetImageSprite(itemImage,ItemConfig:GetIcon(reward.itemId),false)
		itemText.text = "x" .. reward.itemNum
	end
	
	self.ui.m_goBoxCheck.transform:DOScale(Vector3One,0.3)
	SetActive(self.ui.m_btnBack,true)
end

function UI_GiftDaily:GetRewardList(configDefine,id)
	local rewardList = {}
	local config  = ConfigMgr:GetDataByID(ConfigDefine.ID.global_setting, 10602).value
	local table1 = SplitString(config,";")
	

	for k,v in ipairs(table1)do
		local tbReward = SplitStringToNum(v, "|")
		table.insert(rewardList,{itemId = tbReward[1],itemNum = tbReward[2]})
	end
    return rewardList
end

--将Hex颜色码转成color类型
function GiftItem:GetColorByHex(hex)
	local _,color= ColorUtility.TryParseHtmlString("#"..hex,nil);
	return color
end

return UI_GiftDaily