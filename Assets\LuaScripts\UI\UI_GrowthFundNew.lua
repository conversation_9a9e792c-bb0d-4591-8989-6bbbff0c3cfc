local UI_GrowthFundNew = Class(BaseView)

function UI_GrowthFundNew:OnInit()
    self.config = ConfigMgr:GetDataByID(ConfigDefine.ID.growth_fund_new, 1)
    self.level1 = Split1(self.config.level1,"|")
    self.level2 = Split1(self.config.level2,"|")
    self.pay_reward1 = Split2(self.config.pay_reward1, ";", "|")
    self.pay_reward2 = Split2(self.config.pay_reward2, ";", "|")
end

function UI_GrowthFundNew:OnCreate(isPush)
    NetGrowthFundNew:UserUpdate()
    self.isPush = isPush
    self.ItemList = {}
    self.ui.m_txtName.text = LangMgr:GetLang(self.config.name)
    local isPayOne = NetGrowthFundNew:GetDataByKey("PayOne")
    local isGetAllRewardOne = NetGrowthFundNew:GetIsGetAllReward(1)
    local payId = self.config.pay_id1
    if isPayOne and isGetAllRewardOne then
        payId = self.config.pay_id2
    end
    self.payConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.payment, payId)
    self.ui.m_txtPayNum.text = LangMgr:GetLang(self.payConfig.price_langid)
    PurchaseManager:CreateScoreTag(payId,self.ui.m_btnPay.transform)
    self.playCellAnim = true
    self:SetRewardItem()
    self:ResetBtn()

	--self:SetCustomCloseFun(true)
	--DOLocalMoveY(self.ui.m_goTitleBG.transform,527,0.3,nil,Ease.OutQuint)
	--DOLocalMoveX(self.ui.m_goMainBG.transform,0,0.5,nil,Ease.OutQuint)
end

function UI_GrowthFundNew:onCustomCloseFunPlay(playEnd)
	EventMgr:Dispatch(EventID.ACT_ANIM_BEGIN)
	self.customCloseFun = false
	DOLocalMoveY(self.ui.m_goTitleBG.transform,950,0.3,nil,Ease.OutQuint)
	DOLocalMoveX(self.ui.m_goMainBG.transform,1500,0.3,function ()
			EventMgr:Dispatch(EventID.ACT_ANIM_DONE)
			playEnd()
		end,Ease.OutQuint)
end

function UI_GrowthFundNew:PlayCellAnim(obj,k,lastOne,pay_type)

	--for k, v in pairs(self.ItemList) do
	local tran = obj.aniNode.transform
	local time = 0.25 + k*0.1
	--Log.Error(k,obj.aniNode.transform.localPosition.x)
	DOLocalMoveX(obj.aniNode.transform,540,time,function ()
			--DOLocalMoveX(obj.aniNode.transform,200,0.08,nil,Ease.InOutSine)
			if lastOne and self.playCellAnim then
				self.playCellAnim = false
				self:AutoMoveToIndex(pay_type)
			end
	end,Ease.OutSine)
end

function UI_GrowthFundNew:AutoMoveToIndex(pay_type)
	local curIndex = NetGrowthFundNew:GetCurNotGetlRewardIndex(pay_type)
	local moveItem = self.ItemList[curIndex].go:GetComponent(typeof(UE.RectTransform))
	local content = self.ui.m_scrollview.content
	local viewport = self.ui.m_scrollview.viewport
	UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(content)
	local targetValue = moveItem.anchoredPosition.x / (content.rect.width - viewport.rect.width)
	targetValue = Mathf.Clamp01(targetValue)
	Tween.To(function(value)
		if nil == self.ui then
			return 
		end
		self.ui.m_scrollview.horizontalNormalizedPosition = value
	end,0,targetValue,1)
end

--加载奖励列表
function UI_GrowthFundNew:SetRewardItem()
	self.NotGet = 0
	local rewardConfig = self.pay_reward1
	local isPayOne = NetGrowthFundNew:GetDataByKey("PayOne")
	local isGetAllRewardOne = NetGrowthFundNew:GetIsGetAllReward(1)
	local pay_type = 1
	local descId = self.config.explain1
	if isPayOne and isGetAllRewardOne then
		pay_type = 2
		rewardConfig = self.pay_reward2
		descId = self.config.explain2
	end
	self.ui.m_txtDesc.text = LangMgr:GetLang(descId)
	local function callBack(itemListRes)
		for k, v in ipairs(rewardConfig) do
			local go = UEGO.Instantiate(itemListRes)
			if itemListRes then
				go.name = k
				AddChild(self.ui.m_panel,go)
				local itemGo = self:GetItemData(go,k)
				self.ItemList[k] = itemGo
				self:ResetItemData(k)
				self:PlayCellAnim(itemGo,k,k == table.count(rewardConfig),pay_type)
			end
		end
	end
    local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "List_GrowthFund_new")
    ResMgr:LoadAssetAsync(assetPath, AssetDefine.LoadType.Instant,callBack)
end

--整理奖励的组件
function UI_GrowthFundNew:GetItemData(itemGo, index)
    local list = {}
    list.go = itemGo
    list["ImgEffect"] = GetChild(itemGo,"aniNode/Item/ImgEffect")
    list["itemIcon"] = GetChild(itemGo,"aniNode/Item/itemIcon",UEUI.Image)
    list["txtNum"] = GetChild(itemGo,"aniNode/Item/txtNum",UEUI.Text)
    list["LevelTex"] = GetChild(itemGo,"aniNode/Item/level_bg/txtLevel",UEUI.Text)
    list["rewardTag"] = GetChild(itemGo,"aniNode/Item/rewardTag")
    list["txtGet"] = GetChild(itemGo,"aniNode/m_btnGet/m_txtGet",UEUI.Text)
    list["btn"] = GetChild(itemGo, "aniNode/Item/item_bg",UEUI.Button)
    list["m_btnGet"] = GetChild(itemGo, "aniNode/m_btnGet",UEUI.Button)
	list["aniNode"] = GetChild(itemGo,"aniNode")
    --list["btnObj"].name = "btn_"..id.."_"..type
    RemoveUIComponentEventCallback(list["m_btnGet"],UEUI.Button)
    AddUIComponentEventCallback(list["m_btnGet"],UEUI.Button,function (go,param)
        param = {}
        param.index = index
        self:onUIEventClick(go,param)
    end)
	if self.playCellAnim then
		list["aniNode"].transform.localPosition = Vector3(1500,0,0)
	end	
    return list
end

function UI_GrowthFundNew:ResetItemData(index)
    local itemGo = self.ItemList[index]
    if nil == itemGo then
        return
    end
    local levelConfig = self.level1
    local rewardConfig = self.pay_reward1
    local isPayOne = NetGrowthFundNew:GetDataByKey("PayOne")
    local isGetAllRewardOne = NetGrowthFundNew:GetIsGetAllReward(1)
    local pay_type = 1
    if isPayOne and isGetAllRewardOne then
        rewardConfig = self.pay_reward2
        levelConfig = self.level2
        pay_type = 2
    end
    
    itemGo["LevelTex"].text = levelConfig[index]
    local curReward = rewardConfig[index]
    local icon = ItemConfig:GetIcon(curReward.id)
    SetImageSprite(itemGo["itemIcon"],icon,false)
    itemGo["txtNum"].text = curReward.count
    itemGo["txtGet"].text = LangMgr:GetLang(17)
    
    local isGet = NetGrowthFundNew:GetIsGet(index,pay_type)
    local isUnLock = NetGrowthFundNew:GetUnlock(index,pay_type)
    if isGet then
        SetActive(itemGo["rewardTag"],true)
        SetActive(itemGo["m_btnGet"],false)
    else
        SetActive(itemGo["rewardTag"],false)
        SetActive(itemGo["m_btnGet"],true)
        if isUnLock then
            SetUIBtnGrayAndEnable(itemGo["m_btnGet"],true)
        else
            itemGo["txtGet"].text = LangMgr:GetLang(90002)
            SetUIBtnGrayAndEnable(itemGo["m_btnGet"],false)
        end
    end
    RemoveUIComponentEventCallback(itemGo["btn"],UEUI.Button)
    AddUIComponentEventCallback(itemGo["btn"],UEUI.Button,function (go,param)
        local itemId = curReward.id
        if itemId > 0  then
            UI_SHOW(UIDefine.UI_ItemTips,itemId)
        end
    end)
end

function UI_GrowthFundNew:ReSetAllItem()
    local rewardConfig = self.pay_reward1
    local isPayOne = NetGrowthFundNew:GetDataByKey("PayOne")
    local isGetAllRewardOne = NetGrowthFundNew:GetIsGetAllReward(1)
    local payId = self.config.pay_id1
    local descId = self.config.explain1
    if isPayOne and isGetAllRewardOne then
        rewardConfig = self.pay_reward2
        payId = self.config.pay_id2
        descId = self.config.explain2
    end
    self.ui.m_txtDesc.text = LangMgr:GetLang(descId)
    self.payConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.payment, payId)
    self.ui.m_txtPayNum.text = LangMgr:GetLang(self.payConfig.price_langid)
    local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "List_GrowthFund_new")
	
	local function callBack(itemListRes)
		for k, v in ipairs(rewardConfig) do
			if self.ItemList[k] == nil then
				local go = UEGO.Instantiate(itemListRes)
				if itemListRes then
					go.name = k
					AddChild(self.ui.m_panel,go)
					local itemGo = self:GetItemData(go,k)
					self.ItemList[k] = itemGo
				end
			end
			self:ResetItemData(k)
		end
	end
    ResMgr:LoadAssetAsync(assetPath, AssetDefine.LoadType.Instant,callBack)
    
	
end

function UI_GrowthFundNew:ResetBtn()
    local isPayOne = NetGrowthFundNew:GetDataByKey("PayOne")
    local isPayTwo = NetGrowthFundNew:GetDataByKey("PayTwo")
    local isGetAllRewardOne = NetGrowthFundNew:GetIsGetAllReward(1)
    if not isPayOne or (isPayOne and isGetAllRewardOne and not isPayTwo) then
        SetActive(self.ui.m_btnPay,true)
    else
        SetActive(self.ui.m_btnPay,false)
    end
end

function UI_GrowthFundNew:OnRefresh(param)
    --local isOpen = NetGrowthFundNew:IsOpenActivity()
    --if not isOpen then
        --return
    --end
    self:ReSetAllItem()
    self:ResetBtn()
    if param == 1 then
        self:PlayListAnimation()
    end
end


function UI_GrowthFundNew:onDestroy()
    self.config = nil
    self.level1 = nil
    self.level2 = nil
    self.pay_reward1 = nil
    self.pay_reward2 = nil
	
	DOKill(self.ui.m_scrollview.gameObject.transform)
    if not IsTableEmpty(self.ItemList) then
        for i, v in pairs(self.ItemList) do
            if not IsNil(v.go) then
                UEGO.Destroy(v.go)
                self.ItemList[i] = nil
            end
        end
        self.ItemList = nil
    end
    if self.isPush then
        self.isPush = false
        NetPushViewData:RemoveViewByIndex(PushDefine.UI_GrowthFundNew)
        NetPushViewData:CheckOtherView(true)
    end
    if self.tween then
        self.tween:Kill()
        self.tween = nil
    end
	DOKill(self.ui.m_goTitleBG.transform)
	DOKill(self.ui.m_goMainBG.transform)
end

function UI_GrowthFundNew:PlayListAnimation(targetValue)
    targetValue = targetValue or 0
    self.tween = Tween.To(function(value)
        self.ui.m_scrollview.horizontalNormalizedPosition = value
    end,self.ui.m_scrollview.horizontalNormalizedPosition,targetValue,1)
end

function UI_GrowthFundNew:onUIEventClick(go,param)
    local name = go.name
    local function payFunc(double, pos)
        local isPayOne = NetGrowthFundNew:GetDataByKey("PayOne")
        local isGetAllRewardOne = NetGrowthFundNew:GetIsGetAllReward(1)
        local payId = self.config.pay_id1
        if isPayOne and isGetAllRewardOne then
            payId = self.config.pay_id2
        end
        NetGrowthFundNew:PayToVIP(payId)
    end
    if name == "Close" then
        self:Close()
    elseif name == "m_btnPay" then
        payFunc()
    elseif name == "m_btnGet" then
        local rewardConfig = self.pay_reward1
        local isPayOne = NetGrowthFundNew:GetDataByKey("PayOne")
        local isGetAllRewardOne = NetGrowthFundNew:GetIsGetAllReward(1)
        local pay_type = 1
        if isPayOne and isGetAllRewardOne then
            rewardConfig = self.pay_reward2
            pay_type = 2
        end
        if NetGrowthFundNew:GetCanGet(param.index,pay_type) then
            local itemid = rewardConfig[param.index].id
            local count = rewardConfig[param.index].count
            NetGrowthFundNew:SetIsGet(param.index,pay_type)
            NetGrowthFundNew:CheckIsAllGet(pay_type)

            local pos = MapController:GetUIPosByWorld(go.transform.position)
            NetUpdatePlayerData:AddResource(PlayerDefine[itemid], count,nil,nil,"GrowthFundNew")
            MapController:AddResourceBoomAnim(pos.x, pos.y, itemid, count)
            self:OnRefresh()
        else
            payFunc()
        end
    end
end

return UI_GrowthFundNew