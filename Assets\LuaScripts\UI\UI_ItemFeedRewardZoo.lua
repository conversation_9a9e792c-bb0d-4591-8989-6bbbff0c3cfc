local UI_ItemFeedRewardZoo = Class(BaseView)--TD item sure to select
local BubbleItem = require("UI.BubbleItem")
function UI_ItemFeedRewardZoo:OnInit()
    self.m_ObjItem = nil
end
function UI_ItemFeedRewardZoo:OnCreate()
    self.m_ObjItem = ItemConfig:GetDataByID(GlobalConfig.FEED_PROGRESS_ZOO_BOXID)
    self.bubble = BubbleItem.new("UI_ItemFeedRewardZoo")
    self.bubble:Init(self.uiGameObject, {x= 0,y= 0},function()
		self:Getbox()
		self.bubble:NormalItem(self.m_ObjItem.id, 1, self.ui.m_imgIcon.transform.position, 1.7)
	end)

    local function SortOrderAll()
        self:SortOrderAllCom(true)
    end
    EffectConfig:CreateEffect(54, 0, 0, 0, self.ui.m_goEffectBg.transform.transform, function(data, tGo, go)
        SortOrderAll()
	end)
end

function UI_ItemFeedRewardZoo:Getbox()
	SetActive(self.ui.m_imageBg, false)
	SetActive(self.ui.m_goEffectBg, false)
	local feedProgress = NetInfoData:GetDataFeedProgress() - GlobalConfig:GetFeedProgressZooMax()
	NetInfoData:SetDataFeedProgress(feedProgress)
	NetInfoData:GetFirstFeed(1)
	UI_UPDATE(UIDefine.UI_ZooMainFace, 7)
	NetNotification:NotifyNormal(NotifyDefine.ZooGetFeedBox)
	--获得投喂宝箱
	EventMgr:Dispatch(EventID.ANIMAL_EAT_CHEST)
	self:Close()
end

function UI_ItemFeedRewardZoo:OnRefresh(param)

end

function UI_ItemFeedRewardZoo:onDestroy()
end

function UI_ItemFeedRewardZoo:onUIEventClick(go,param)
    local name = go.name
	--self:Getbox()
	if self.bubble:IsHaveItem() then
		self.bubble:Close()
	else
		self:Close()
	end
end

return UI_ItemFeedRewardZoo