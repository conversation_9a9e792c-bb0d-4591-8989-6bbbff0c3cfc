local UI_TradeWagonsTrainDetail = Class(BaseView)
local SlideRect = require("UI.Common.SlideRect")
local ItemBase = require("UI.Common.BaseSlideItem")
local FightLogMeItem = Class(ItemBase)
local FightLogOtherItem = Class(ItemBase)
local ItemShowNums = 4
local GoTradeTrain = require("UI.GoTradeTrain")
local GoSlgHeroItem = require("UI.GoSlgHeroItem")

local bgMoveSpeed = 1
local bgOffset = 34

local DetailType
local DetailData

local TimerStr = ""

local PlunderBgStr = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_leibiao2.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_leibiao.png",
}

local PlunderTitleBgStr = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_leibiao_chenggong.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_leibiao_shibai.png",
}

local WantedImgStr = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_tongji1.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win3_tongji2.png",
}

local WantedPlayer = {}

function UI_TradeWagonsTrainDetail:OnInit()

end

function UI_TradeWagonsTrainDetail:OnCreate(type, trainData, callback)
    self.type = type
    self.trainData = trainData
    self.callback = callback
    DetailType = type
    DetailData = trainData
    TimerStr = LangMgr:GetLang(70000449)
    WantedPlayer = {}

    if trainData then
        self.departTime = trainData.depart_timestamp or 0
        self.endTime = trainData.end_timestamp or 0
    else
        self.departTime = 0
        self.endTime = 0
    end

    self.canMoveBg = true
    self.bgRect = GetComponent(self.ui.m_imgBg, UE.RectTransform)
    self.bgRect2 = GetComponent(self.ui.m_imgBg2, UE.RectTransform)
    self.firstBgRect = self.bgRect
    self.secondBgRect = self.bgRect2
    self.firstMoveX = self.firstBgRect.rect.width
    self.secondmoveX = self.secondBgRect.rect.width
    self.firstPosition = self.bgRect.transform.localPosition
    self.secondPosition = self.bgRect2.transform.localPosition

    local transform = self.ui.m_goBg
    self.battle_shaoguang_n = GetChild(transform, "top/battle_shaoguang_n")
    self.battle_shaoguang_r = GetChild(transform, "top/battle_shaoguang_r")
    self.battle_shaoguang_sr = GetChild(transform, "top/battle_shaoguang_sr")
    self.battle_shaoguang_ssr = GetChild(transform, "top/battle_shaoguang_ssr")
    self.battle_shaoguang_ur = GetChild(transform, "top/battle_shaoguang_ur")

    self.trainRewardBorder = {}
    self.heroItemList = {}
    for i = 1, 3, 1 do
        self.heroItemList[i] = {}
    end

    SetActive(self.ui.m_goTeamDetail, false)
    SetActive(self.ui.m_goTeamDetailItem, false)

    self:InitPanel()
    self:InitFightLogMeList()
    self:InitFightLogOtherList()
    self:InitTrain()
    self:RefreshTeamInfo(self.trainData.teams)

    self:RefreshPanel()

    self.ui.m_txtTip.text = LangMgr:GetLangFormat(70001049, LangMgr:GetLang(70000448))

    self:SetIsUpdateTick(true)
end

function UI_TradeWagonsTrainDetail:OnRefresh(type)
    if type == 1 then
        self.trainData = TradeWagonsManager.trainLoadInfo.can_loot_train
        self:RefreshPanel()
    end
end

function UI_TradeWagonsTrainDetail:onDestroy()
    self:SetIsUpdateTick(false)
    WantedPlayer = {}
end

function UI_TradeWagonsTrainDetail:onUIEventClick(go)
    local name = go.name
    -- 关闭按钮
    if name == "m_btnClose" then
        self:Close()
    -- 提示按钮
    elseif name == "m_btnTip" then
        SetActive(self.ui.m_goTip, true)
        SetActive(self.ui.m_btnMask, true)
    -- 查看/编辑队伍按钮
    elseif name == "m_btnCheckTeam" then
        SetActive(self.ui.m_goTeamDetail, true)
        SetActive(self.ui.m_btnMask, true)
    -- 关闭提示的遮罩按钮
    elseif name == "m_btnMask" then
        SetActive(self.ui.m_goTip, false)
        SetActive(self.ui.m_goTeamDetail, false)
        SetActive(self.ui.m_btnMask, false)
    -- 分享按钮
    elseif name == "m_btnShare" then
        if self.trainData then
            UI_SHOW(UIDefine.UI_TradeWagonsShare,self.trainData)
        end
    -- 掠夺按钮
    elseif name == "m_btnPlunder" then
        local plunderCountMaxSingle = v2n(TradeWagonsManager:GetTradeSettingConfig(34))
        if self.trainData.train_attacked_times >= plunderCountMaxSingle then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000470))
            return
        end

        local plunderCount = TradeWagonsManager:GetPlunderCount()
        local plunderCountMax = v2n(TradeWagonsManager:GetTradeSettingConfig(6))
        if plunderCount >= plunderCountMax then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000678))
            return
        end

        local endRemain = self.endTime - TimeMgr:GetServerTime()
        if endRemain >= 0 then
            local cannotPlunderTime = v2n(TradeWagonsManager:GetTradeSettingConfig(10))
            if endRemain <= cannotPlunderTime then
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000470))
                -- TradeWagonsManager:ClearCanLootTrain()
                UI_UPDATE(UIDefine.UI_TradeWagonsView, 3)
            else
                UI_SHOW(UIDefine.UI_TradeWagonsTrainBattlePlan, self.trainData)
            end
        else
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000489))
            TradeWagonsManager:ClearCanLootTrain()
            UI_UPDATE(UIDefine.UI_TradeWagonsView, 3)
        end
    end
end

function UI_TradeWagonsTrainDetail:TickUI(deltaTime)
    if self.canMoveBg then
        self:StartMoveBg(deltaTime)
    end

    if not self.departTime then
        return
    end
    local remain = self.departTime - TimeMgr:GetServerTime()
    if remain >= 0 then
        self.ui.m_txtTime.text = TimerStr .. TimeMgr:ConverSecondToString(remain)
    else
        local endRemain = self.endTime - TimeMgr:GetServerTime()
        if endRemain >= 0 then
            self.ui.m_txtTime.text = TimerStr .. TimeMgr:ConverSecondToString(endRemain)
        else
            self.ui.m_txtTime.text = TimerStr .. TimeMgr:ConverSecondToString(0)
        end
    end
end

--- 初始化界面
function UI_TradeWagonsTrainDetail:InitPanel()
    -- 切换掠夺记录滚动视图
    SetActive(self.ui.m_scrollviewMe, self.type == TRADE_WAGONS_DETAIL.Me)
    SetActive(self.ui.m_scrollviewOther, self.type == TRADE_WAGONS_DETAIL.Other)
end

function UI_TradeWagonsTrainDetail:RefreshPanel()
    if not self.trainData then return end
    self:RefreshInfo(self.trainData)
    self:RefreshTrainReward(self.trainData)
    self:RefreshTeamFightInfo(self.trainData.teams)
end

function UI_TradeWagonsTrainDetail:RefreshInfo(data)
    local quality = 5
    for _, value in pairs(self.trainData.train_quality) do
        if value.part == 0 then
            quality = value.quality
        end
    end
    SetUIImage(self.ui.m_imgQuality, TradeWagonsManager:GetQualityIcon(quality), true)

    local completenessPerPlunder = v2n(TradeWagonsManager:GetTradeSettingConfig(44))
    local completeness = 100 - data.train_attacked_times * completenessPerPlunder
    self.ui.m_txtCompleteness.text = completeness .. "%"

    local plunderMax = v2n(TradeWagonsManager:GetTradeSettingConfig(34))
    self.ui.m_txtPlunderCount.text = string.format("%s/%s", plunderMax - data.train_attacked_times, plunderMax)

    SetActive(self.battle_shaoguang_n, quality == 1)
    SetActive(self.battle_shaoguang_r, quality == 2)
    SetActive(self.battle_shaoguang_sr, quality == 3)
    SetActive(self.battle_shaoguang_ssr, quality == 4)
    SetActive(self.battle_shaoguang_ur, quality == 5)

    if self.type == TRADE_WAGONS_DETAIL.Me then
        SetUIPos(self.ui.m_btnShare, 200, 0)
        SetActive(self.ui.m_btnPlunder, false)
        if self.trainData.status == TRADE_TRAIN_STATUS.Transporting then
            self.ui.m_txtCheckTeam.text = LangMgr:GetLang(70000477)
        else
            self.ui.m_txtCheckTeam.text = LangMgr:GetLang(70000465)
        end
        self.ui.m_txtPlunder.text = LangMgr:GetLang(70000447)
        table.sort(data.battle_logs, function (a, b)
            if a.battle_timestamp ~= b.battle_timestamp then
                return a.battle_timestamp > b.battle_timestamp
            end
            return false
        end)
        self:RefreshFightLogMeList(data.battle_logs)
        SetActive(self.ui.m_goEmpty, #data.battle_logs == 0)
    elseif self.type == TRADE_WAGONS_DETAIL.Other then
        SetUIPos(self.ui.m_btnShare, 0, 0)
        SetActive(self.ui.m_btnPlunder, true)
        self.ui.m_txtCheckTeam.text = LangMgr:GetLang(70000477)
        self.ui.m_txtPlunder.text = LangMgr:GetLang(70000447)
        table.sort(data.battle_logs, function (a, b)
            if a.battle_timestamp ~= b.battle_timestamp then
                return a.battle_timestamp > b.battle_timestamp
            end
            return false
        end)
        self:RefreshFightLogOtherList(data.battle_logs)
        SetActive(self.ui.m_goEmpty, #data.battle_logs == 0)
    end
end

--region ----------------------------------------- 火车 -----------------------------------------

--- 初始化火车
function UI_TradeWagonsTrainDetail:InitTrain()
    self.goTradeTrain = GoTradeTrain:Create(self.ui.m_goTradeTrain)

    -- 等待加载完成后再进行初始化
    self.goTradeTrain:WaitForLoad(function(trainInstance)
        if IsTableNotEmpty(self.trainData) then
            local quality = {}
            for _, value in pairs(self.trainData.train_quality) do
                if value.part > 0 then
                    quality[value.part] = value.quality
                end
            end
            trainInstance:SetBodyIconByQualityList(quality)
        end

        trainInstance:CheckGold(self.trainData)

        self.train = GetComponent(trainInstance.go, UE.RectTransform)
        self.headNode = trainInstance.headNode
        SetMyHeadAndBorderByGo(self.headNode)
        local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
        if self.trainData.captain.id == myPlayerInfo.id then
            local myCaptain = {
                name = myPlayerInfo.name,
                icon = myPlayerInfo.head,
                border = myPlayerInfo.headBorder
            }
            trainInstance:SetConductorInfo(myCaptain)
        else
            trainInstance:SetConductorInfo(self.trainData.captain)
        end
    end)

    for i = 1, 5, 1 do
        local pos = GetChild(self.ui.m_goRewardPos, "pos" .. i)
        if pos.transform.childCount == 0 then
            local item = CreateGameObjectWithParent(self.ui.m_goTrainBorderItem, pos)
            SetUIPos(item, 0, 0)
            SetActive(item, true)
            self.trainRewardBorder[i] = item
        end
    end
end

--- 刷新火车奖励
--- @param data table 奖励信息
function UI_TradeWagonsTrainDetail:RefreshTrainReward(data)
    for _, value in ipairs(data.rewards_list) do
        local train_part = value.train_part
        local rewardItem = self.ui.m_goTrainRewardItem
        local borderItem = self.trainRewardBorder[train_part + 1]
        local borderItemContent = GetChild(borderItem, "border/Scroll View/Viewport/Content")
        local border = GetChild(borderItem, "border")
        local arrowTop = GetChild(borderItem, "border/arrowTop")
        local arrowBottom = GetChild(borderItem, "border/arrowBottom")
        if (train_part + 1) % 2 == 1 then
            SetUIPos(border, 0, 347)
            SetActive(arrowTop, false)
            SetActive(arrowBottom, true)
        else
            SetUIPos(border, 0, 0)
            SetActive(arrowTop, true)
            SetActive(arrowBottom, false)
        end

        local rewardItemCount = borderItemContent.transform.childCount
        -- 先全部隐藏
        for i = 1, rewardItemCount, 1 do
            local item = borderItemContent.transform:GetChild(i - 1)
            SetActive(item, false)
        end
        -- 显示奖励
        for key, rewardValue in ipairs(value.rewards) do
            local itemID = rewardValue.reward.code
            local itemNum = rewardValue.reward.amount
            local item
            -- 有可用的 item 直接获取
            if key <= rewardItemCount then
                item = borderItemContent.transform:GetChild(key - 1)
            -- item 不够用，创建新的
            else
                item = CreateGameObjectWithParent(rewardItem, borderItemContent)
            end
            -- 底框
            local border = GetComponent(item, UEUI.Image)
            local borderIconPath = TradeWagonsManager:GetRewardQualityIcon(ItemConfig:GetSlgQuality(itemID))
            SetUIImage(border, borderIconPath, false)
            -- 图标
            local icon = GetChild(item, "icon", UEUI.Image)
            local iconPath = ItemConfig:GetIcon(itemID)
            SetUIImage(icon, iconPath, false)
            -- 数量
            local textNum = GetChild(item, "num", UEUI.Text)
            textNum.text = "x" .. NumToGameString(itemNum)
            -- 按钮
            local button = GetChild(item, "icon", UEUI.Button)
            button.onClick:RemoveAllListeners()
            button.onClick:AddListener(function ()
                UI_SHOW(UIDefine.UI_ItemTips, itemID)
            end)
            -- 被掠夺状态
            local mask = GetChild(item, "mask")
            local missState = rewardValue.status == TRADE_WAGONS_REWARD_STATUS.Lost
            SetActive(mask, missState)
            SetActive(item, true)
        end
    end

    for _, value in ipairs(data.train_quality) do
        local train_part = value.part
        local borderItem = self.trainRewardBorder[train_part + 1]
        local borderItemContent = GetChild(borderItem, "border/Scroll View/Viewport/Content")
        local border = GetChild(borderItem, "border")
        if train_part == TRADE_TRAIN_CARRIAGE.Head then
            SetUISize(border, 244, 165)
            local gridLayoutGroup = GetComponent(borderItemContent, UEUI.GridLayoutGroup)
            gridLayoutGroup.constraintCount = 3
        else
            SetUISize(border, 170, 165)
            local gridLayoutGroup = GetComponent(borderItemContent, UEUI.GridLayoutGroup)
            gridLayoutGroup.constraintCount = 2
        end

        local quality = value.quality
        local rewardBg = GetChild(borderItem, "border", UEUI.Image)
        local arrowTop = GetChild(borderItem, "border/arrowTop")
        local arrowBottom = GetChild(borderItem, "border/arrowBottom")
        SetUIImage(rewardBg, TradeWagonsManager:GetRewardBgIcon(quality), false)
        SetUIImage(arrowTop, TradeWagonsManager:GetRewardArrowIcon(quality), false)
        SetUIImage(arrowBottom, TradeWagonsManager:GetRewardArrowIcon(quality), false)
    end

    for i = 1, 5, 1 do
        local value

        for _, passegers in ipairs(data.passegers_list) do
            if passegers.train_part == i - 1 then
                value = passegers
            end
        end

        if not value then
            value = {
                train_part = i - 1,
                passegers = {}
            }
        end

        local train_part = value.train_part
        local borderItem = self.trainRewardBorder[train_part + 1]
        local btnPassenger = GetChild(borderItem, "PeopleNum/peopleNum", UEUI.Button)
        btnPassenger.onClick:RemoveAllListeners()
        btnPassenger.onClick:AddListener(function ()
            UI_SHOW(UIDefine.UI_TradeWagonsTrainPassenger, value.passegers)
        end)
        -- 乘客数量
        local curPeopleNum = #value.passegers
        local maxPeopleNum = v2n(TradeWagonsManager:GetTradeSettingConfig(36))
        local numTxt = GetChild(borderItem, "PeopleNum/peopleNum/num", UEUI.Text)
        numTxt.text = string.format("%s/%s", curPeopleNum, maxPeopleNum)

        if train_part == TRADE_TRAIN_CARRIAGE.Head then
            SetActive(btnPassenger, false)
        end
    end
end

--- 刷新队伍信息
---@param data table 队伍信息
function UI_TradeWagonsTrainDetail:RefreshTeamInfo(data)

    table.sort(data, function (a, b)
        if a.team_type ~= b.team_type then
            return a.team_type < b.team_type
        end
        return false
    end)

    local parent = GetChild(self.ui.m_goTeamDetail, "middle/Scroll View/Viewport/Content")

    -- 先全部隐藏
    for i = 1, parent.transform.childCount, 1 do
        local item = parent.transform:GetChild(i - 1)
        SetActive(item, false)
    end

    for index = 1, 3, 1 do
        local item
        -- 有可用的 item 直接获取
        if index <= parent.transform.childCount then
            item = parent.transform:GetChild(index - 1)
        -- item 不够用，创建新的
        else
            item = CreateGameObjectWithParent(self.ui.m_goTeamDetailItem, parent)
        end

        SetActive(item, true)

        -- 编号
        local txtNum = GetChild(item, "border/icon/text", UEUI.Text)
        txtNum.text = index

        local txtFight = GetChild(item, "border/text", UEUI.Text)

        local team = data[index]

        if team then
            -- 战力
            local totalPower = TradeWagonsManager:GetBattleListTotalPower(team.heroes)
            -- for _, hero in ipairs(team.heroes) do
            --     fight = fight + hero.power
            -- end
            txtFight.text = NumToGameString(totalPower)
            -- 英雄
            for _, value in ipairs(self.heroItemList[index]) do
                SetActive(value.go, false)
            end
            local heroParent = GetChild(item, "Scroll View/Viewport/Content")
            for key, value in ipairs(team.heroes) do
                local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_hero, value.code)
                if config then
                    local hero = HeroModule.new(value.code, config)
                    hero:SetHeroValueByKey("level", value.level)
                    hero:SetHeroValueByKey("starLv", value.star)
                    local heroGo
                    if key <= #self.heroItemList[index] then
                        heroGo = self.heroItemList[index][key]
                        heroGo:ChangHero(hero)
                        SetActive(heroGo.go, true)
                    else
                        heroGo = GoSlgHeroItem:Create(heroParent, hero)
                        heroGo:SetIsNeedShowSelect(false)
                        heroGo:SetItem()
                        heroGo:SetScale(0.59, 0.59)
                        table.insert(self.heroItemList[index], heroGo)
                    end
                end
            end
        else
            txtFight.text = NumToGameString(0)
        end
    end
end

--- 刷新队伍战力信息
--- @param data table 队伍信息
function UI_TradeWagonsTrainDetail:RefreshTeamFightInfo(data)
    local fightList = {}
    for _, team in ipairs(data) do
        local fight = 0
        for _, hero in ipairs(team.heroes) do
            fight = fight + hero.power
        end
        table.insert(fightList, {
            fight = fight,
            team_type = team.team_type,
            heroes = team.heroes
        })
    end

    table.sort(fightList, function (a, b)
        if a.team_type ~= b.team_type then
            return a.team_type < b.team_type
        end
        return false
    end)

    local fight1 = fightList[1]
    local fight2 = fightList[2]
    local fight3 = fightList[3]

    if fight1 then
        local totalPower = TradeWagonsManager:GetBattleListTotalPower(fight1.heroes)
        self.ui.m_txtTeamFight1.text = NumToGameString(totalPower)
    else
        self.ui.m_txtTeamFight1.text = 0
    end

    if fight2 then
        local totalPower = TradeWagonsManager:GetBattleListTotalPower(fight2.heroes)
        self.ui.m_txtTeamFight2.text = NumToGameString(totalPower)
    else
        self.ui.m_txtTeamFight2.text = 0
    end

    if fight3 then
        local totalPower = TradeWagonsManager:GetBattleListTotalPower(fight3.heroes)
        self.ui.m_txtTeamFight3.text = NumToGameString(totalPower)
    else
        self.ui.m_txtTeamFight3.text = 0
    end
end

--endregion -------------------------------------- 火车 -----------------------------------------

--region ----------------------------------------- 背景滚动 -----------------------------------------

function UI_TradeWagonsTrainDetail:StartMoveBg(deltaTime)
    self:MoveBg(self.ui.m_imgBg.transform, bgMoveSpeed, 1, deltaTime)
    self:MoveBg(self.ui.m_imgBg2.transform, bgMoveSpeed, 2, deltaTime)
end

function UI_TradeWagonsTrainDetail:MoveBg(rect, speed, type, deltaTime)
    local length = 0
    if type == 1 then
        length = -1 * (self.firstMoveX)
    elseif type == 2 then
        length = -1 * (self.secondmoveX)
    end
    local rectX = rect.anchoredPosition.x
    if rectX >= length then
        rect.transform:Translate(Vector3.left * speed * deltaTime)
    else
        self.firstBgRect,self.secondBgRect = self.secondBgRect, self.firstBgRect
        self:ChangeBgPositon(type)
    end
end

function UI_TradeWagonsTrainDetail:ChangeBgPositon(type)
    local firstRectX = self.firstBgRect.anchoredPosition.x - bgOffset
    if type == 1 then
        firstRectX = firstRectX + self.secondmoveX
    elseif type == 2 then
        firstRectX = firstRectX + self.firstMoveX
    end

    self.secondBgRect.anchoredPosition = Vector2.New(firstRectX, -133)
end

--endregion -------------------------------------- 背景滚动 -----------------------------------------

--region ----------------------------------------- 战斗记录 -----------------------------------------

function UI_TradeWagonsTrainDetail:InitFightLogMeList()
    SetActive(self.ui.m_goFightLogMe, false)
    self.fightLogMeSlider = SlideRect.new()
    self.fightLogMeSlider:Init(self.ui.m_scrollviewMe, 2)
    self.fightLogMeList = {}
    for i = 1, ItemShowNums do
        self.fightLogMeList[i] = FightLogMeItem.new()
        self.fightLogMeList[i]:Init(UEGO.Instantiate(self.ui.m_goFightLogMe.transform))
    end
    self.fightLogMeSlider:SetItems(self.fightLogMeList, 7, Vector2.New(0, 0))
end

function UI_TradeWagonsTrainDetail:RefreshFightLogMeList(data)
    self.fightLogMeSlider:SetData(data)
end

function UI_TradeWagonsTrainDetail:InitFightLogOtherList()
    SetActive(self.ui.m_goFightLogOther, false)
    self.fightLogOtherSlider = SlideRect.new()
    self.fightLogOtherSlider:Init(self.ui.m_scrollviewOther, 2)
    self.fightLogOtherList = {}
    for i = 1, ItemShowNums do
        self.fightLogOtherList[i] = FightLogOtherItem.new()
        self.fightLogOtherList[i]:Init(UEGO.Instantiate(self.ui.m_goFightLogOther.transform))
    end
    self.fightLogOtherSlider:SetItems(self.fightLogOtherList, 7, Vector2.New(0, 0))
end

function UI_TradeWagonsTrainDetail:RefreshFightLogOtherList(data)
    self.fightLogOtherSlider:SetData(data)
end

function FightLogMeItem:OnInit(transform)
    -- 创建头像
    local head = GetChild(transform, "head")
    CreateCommonHeadAsync(head, 0.43, nil, function (go, trans)
        self.headNode = go
        SetMyHeadAndBorderByGo(self.headNode)
    end)

    self.imgBg = GetChild(transform, "bg", UEUI.Image)
    self.txtTime = GetChild(transform, "txtTime", UEUI.Text)
    self.txtResult = GetChild(transform, "txtResult", UEUI.Text)
    self.txtName = GetChild(transform, "txtName", UEUI.Text)
    self.txtFight = GetChild(transform, "txtFight", UEUI.Text)

    -- self.imgQuality = GetChild(transform, "imgQuality", UEUI.Image)

    self.imgLeft = GetChild(transform, "left", UEUI.Image)

    self.btnPlayback = GetChild(transform, "btnPlayback", UEUI.Button)
    self.btnPlayback.onClick:AddListener(function ()
        UI_SHOW(UIDefine.UI_TradeWagonsBattleTrainDetail, self.data, DetailData, DetailType)
    end)

    self.rewardItem = GetChild(transform, "rewardItem")
    self.rewardScrollRect = GetChild(transform, "Scroll View", UEUI.ScrollRect)
    self.rewardContent = GetChild(transform, "Scroll View/Viewport/Content")

    self.imgWanted = GetChild(transform, "btnWanted/icon", UEUI.Image)
    self.txtWanted = GetChild(transform, "btnWanted/txtWanted", UEUI.Text)
    self.btnWanted = GetChild(transform, "btnWanted", UEUI.Button)
    self.btnWanted.onClick:AddListener(function ()
        if self.data.wanted_status == 0 then
            TradeWagonsManager:RequestWanted(self.data.battle_record_id, function (data)
                local wantedImgIndex
                if data.status == 0 then
                    wantedImgIndex = 1
                    self.txtWanted.text = LangMgr:GetLang(70000467)
                    self.txtWanted.color = Color.HexToRGB("ffffff")
                    UnifyOutline(self.txtWanted, "850000")
                else
                    wantedImgIndex = 2
                    self.txtWanted.text = LangMgr:GetLang(70000468)
                    self.txtWanted.color = Color.HexToRGB("999999")
                    UnifyOutline(self.txtWanted, "500000")
                end
                SetUIImage(self.imgWanted, WantedImgStr[wantedImgIndex], false)
                self.data.wanted_status = data.status
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000507))

                WantedPlayer[self.data.attacker_player.id] = true

                UI_UPDATE(UIDefine.UI_TradeWagonsDetail, 1)
            end)
        end
    end)
end

function FightLogMeItem:UpdateData(data, index)
    if not data then return end
    self.data = data
    self.index = index

    local player = data.attacker_player
    if player then
        SetHeadAndBorderByGo(self.headNode, player.icon, player.border, function ()
            if player and not IsNilOrEmpty(player.id) then
                FriendManager:ShowPlayerById(player.id)
            end
        end)

        self.txtName.text = player.name

        local playerInfo = NetUpdatePlayerData:GetPlayerInfo()
        if playerInfo.id == player.id then
            self.txtName.text = playerInfo.name
        end

        self.txtFight.text = NumToGameString(player.power)
    end

    self.txtTime.text = os.date("%Y-%m-%d %H:%M:%S", data.battle_timestamp)

    local txtResultLang
    local plunderBgIndex

    local fight_result
    local winCount = 0
    for _, value in ipairs(data.battle_result_info) do
        if value.battle_result == 1 then
            winCount = winCount + 1
        end
    end

    if winCount >= 2 then
        fight_result = 1
    else
        fight_result = 0
    end

    -- 防守失败
    if fight_result == 1 then
        if DetailType == TRADE_WAGONS_DETAIL.Me then
            txtResultLang = 70000452
            plunderBgIndex = 2
            self.txtResult.color = Color.HexToRGB("FFFFFF")
            UnifyOutline(self.txtResult, "#424752")
            -- SetActive(self.btnWanted, true)
        elseif DetailType == TRADE_WAGONS_DETAIL.Other then
            txtResultLang = 70000455
            plunderBgIndex = 1
            self.txtResult.color = Color.HexToRGB("FFE57F")
            UnifyOutline(self.txtResult, "#8D2200")
        end
    -- 防守成功
    else
        if DetailType == TRADE_WAGONS_DETAIL.Me then
            txtResultLang = 70000453
            plunderBgIndex = 1
            self.txtResult.color = Color.HexToRGB("FFE57F")
            UnifyOutline(self.txtResult, "#8D2200")
            SetActive(self.btnWanted, false)
        elseif DetailType == TRADE_WAGONS_DETAIL.Other then
            txtResultLang = 70000454
            plunderBgIndex = 2
            self.txtResult.color = Color.HexToRGB("FFFFFF")
            UnifyOutline(self.txtResult, "#424752")
        end
    end

    self.txtResult.text = LangMgr:GetLang(txtResultLang)

    SetUIImage(self.imgBg, PlunderBgStr[plunderBgIndex], false)
    SetUIImage(self.imgLeft, PlunderTitleBgStr[plunderBgIndex], false)

    local wantedImgIndex
    if data.wanted_status == 0 then
        wantedImgIndex = 1
        self.txtWanted.text = LangMgr:GetLang(70000467)
        self.txtWanted.color = Color.HexToRGB("ffffff")
        UnifyOutline(self.txtWanted, "850000")
    else
        wantedImgIndex = 2
        self.txtWanted.text = LangMgr:GetLang(70000468)
        self.txtWanted.color = Color.HexToRGB("999999")
        UnifyOutline(self.txtWanted, "500000")
        if self.data.attacker_player then
            WantedPlayer[self.data.attacker_player.id] = true
        end
    end
    SetUIImage(self.imgWanted, WantedImgStr[wantedImgIndex], false)

    if self.data.attacker_player and WantedPlayer[self.data.attacker_player.id] then
        wantedImgIndex = 2
        self.txtWanted.text = LangMgr:GetLang(70000468)
        self.txtWanted.color = Color.HexToRGB("999999")
        UnifyOutline(self.txtWanted, "500000")
        SetUIImage(self.imgWanted, WantedImgStr[wantedImgIndex], false)
    end

    self:RefreshReward(data.robed_rewards)

    self.rewardScrollRect.horizontalNormalizedPosition = 0
end

function FightLogMeItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function FightLogMeItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function FightLogMeItem:RefreshReward(data)
    local content = self.rewardContent
    local rewardItem = self.rewardItem

    local rewardItemCount = content.transform.childCount
    -- 先全部隐藏
    for i = 1, rewardItemCount, 1 do
        local item = content.transform:GetChild(i - 1)
        SetActive(item, false)
    end

    -- 再根据奖励列表显示
    for key, value in pairs(data) do
        local itemID = value.code
        local itemNum = value.amount

        local item
        -- 有可用的 item 直接获取
        if key <= rewardItemCount then
            item = content.transform:GetChild(key - 1)
        -- item 不够用，创建新的
        else
            item = CreateGameObjectWithParent(rewardItem, content)
        end

        -- 底框
        local border = GetComponent(item, UEUI.Image)
        local borderIconPath = TradeWagonsManager:GetRewardQualityIcon(ItemConfig:GetSlgQuality(itemID))
        SetUIImage(border, borderIconPath, false)
        -- 图标
        local icon = GetChild(item, "icon", UEUI.Image)
        local iconPath = ItemConfig:GetIcon(itemID)
        SetUIImage(icon, iconPath, false)
        -- 数量
        local textNum = GetChild(item, "num", UEUI.Text)
        textNum.text = "x" .. NumToGameString(itemNum)
        -- 按钮
        local button = GetChild(item, "icon", UEUI.Button)
        button.onClick:RemoveAllListeners()
        button.onClick:AddListener(function ()
            UI_SHOW(UIDefine.UI_ItemTips, itemID)
        end)

        -- 被掠夺状态
        local mask = GetChild(item, "mask")
        local missState = value.status == TRADE_WAGONS_REWARD_STATUS.Lost
        SetActive(mask, missState)

        SetActive(item, true)
    end
end

function FightLogMeItem:RefreshWanted()
    local wantedImgIndex
    if not self.data then return end
    if self.data.wanted_status == 0 then
        wantedImgIndex = 1
        self.txtWanted.text = LangMgr:GetLang(70000467)
        self.txtWanted.color = Color.HexToRGB("ffffff")
        UnifyOutline(self.txtWanted, "850000")
    else
        wantedImgIndex = 2
        self.txtWanted.text = LangMgr:GetLang(70000468)
        self.txtWanted.color = Color.HexToRGB("999999")
        UnifyOutline(self.txtWanted, "500000")
    end
    SetUIImage(self.imgWanted, WantedImgStr[wantedImgIndex], false)

    if WantedPlayer[self.data.attacker_player.id] then
        wantedImgIndex = 2
        self.txtWanted.text = LangMgr:GetLang(70000468)
        self.txtWanted.color = Color.HexToRGB("999999")
        UnifyOutline(self.txtWanted, "500000")
        SetUIImage(self.imgWanted, WantedImgStr[wantedImgIndex], false)
    end
end

function FightLogOtherItem:OnInit(transform)
    -- 创建头像
    local head = GetChild(transform, "head")
    CreateCommonHeadAsync(head, 0.43, nil, function (go, trans)
        self.headNode = go
        SetMyHeadAndBorderByGo(self.headNode)
    end)

    self.imgBg = GetChild(transform, "bg", UEUI.Image)
    self.txtTime = GetChild(transform, "txtTime", UEUI.Text)
    self.txtResult = GetChild(transform, "txtResult", UEUI.Text)
    self.txtName = GetChild(transform, "txtName", UEUI.Text)
    self.txtFight = GetChild(transform, "txtFight", UEUI.Text)

    -- self.imgQuality = GetChild(transform, "imgQuality", UEUI.Image)

    self.imgLeft = GetChild(transform, "left", UEUI.Image)

    self.btnPlayback = GetChild(transform, "btnPlayback", UEUI.Button)
    self.btnPlayback.onClick:AddListener(function ()
        UI_SHOW(UIDefine.UI_TradeWagonsBattleTrainDetail, self.data, DetailData, DetailType)
    end)

    self.rewardItem = GetChild(transform, "rewardItem")
    self.rewardScrollRect = GetChild(transform, "Scroll View", UEUI.ScrollRect)
    self.rewardContent = GetChild(transform, "Scroll View/Viewport/Content")
end

function FightLogOtherItem:UpdateData(data, index)
    if not data then return end
    self.data = data
    self.index = index

    local player = data.attacker_player
    if player then
        SetHeadAndBorderByGo(self.headNode, player.icon, player.border, function ()
            if player and not IsNilOrEmpty(player.id) then
                FriendManager:ShowPlayerById(player.id)
            end
        end)

        self.txtName.text = player.name

        local playerInfo = NetUpdatePlayerData:GetPlayerInfo()
        if playerInfo.id == player.id then
            self.txtName.text = playerInfo.name
        end

        self.txtFight.text = NumToGameString(player.power)
    end

    self.txtTime.text = os.date("%Y-%m-%d %H:%M:%S", data.battle_timestamp)

    local txtResultLang
    local plunderBgIndex

    local fight_result
    local winCount = 0
    for _, value in ipairs(data.battle_result_info) do
        if value.battle_result == 1 then
            winCount = winCount + 1
        end
    end

    if winCount >= 2 then
        fight_result = 1
    else
        fight_result = 0
    end

    -- 防守失败
    if fight_result == 1 then
        if DetailType == TRADE_WAGONS_DETAIL.Me then
            txtResultLang = 70000452
            plunderBgIndex = 2
            self.txtResult.color = Color.HexToRGB("FFFFFF")
            UnifyOutline(self.txtResult, "#424752")
        elseif DetailType == TRADE_WAGONS_DETAIL.Other then
            txtResultLang = 70000455
            plunderBgIndex = 1
            self.txtResult.color = Color.HexToRGB("FFE57F")
            UnifyOutline(self.txtResult, "#8D2200")
        end
    -- 防守成功
    else
        if DetailType == TRADE_WAGONS_DETAIL.Me then
            txtResultLang = 70000453
            plunderBgIndex = 1
            self.txtResult.color = Color.HexToRGB("FFE57F")
            UnifyOutline(self.txtResult, "#8D2200")
        elseif DetailType == TRADE_WAGONS_DETAIL.Other then
            txtResultLang = 70000454
            plunderBgIndex = 2
            self.txtResult.color = Color.HexToRGB("FFFFFF")
            UnifyOutline(self.txtResult, "#424752")
        end
    end

    self.txtResult.text = LangMgr:GetLang(txtResultLang)

    SetUIImage(self.imgBg, PlunderBgStr[plunderBgIndex], false)
    SetUIImage(self.imgLeft, PlunderTitleBgStr[plunderBgIndex], false)

    self:RefreshReward(data.robed_rewards)

    self.rewardScrollRect.horizontalNormalizedPosition = 0
end

function FightLogOtherItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function FightLogOtherItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function FightLogOtherItem:RefreshReward(data)
    local content = self.rewardContent
    local rewardItem = self.rewardItem

    local rewardItemCount = content.transform.childCount
    -- 先全部隐藏
    for i = 1, rewardItemCount, 1 do
        local item = content.transform:GetChild(i - 1)
        SetActive(item, false)
    end

    -- 再根据奖励列表显示
    for key, value in pairs(data) do
        local itemID = value.code
        local itemNum = value.amount

        local item
        -- 有可用的 item 直接获取
        if key <= rewardItemCount then
            item = content.transform:GetChild(key - 1)
        -- item 不够用，创建新的
        else
            item = CreateGameObjectWithParent(rewardItem, content)
        end

        -- 底框
        local border = GetComponent(item, UEUI.Image)
        local borderIconPath = TradeWagonsManager:GetRewardQualityIcon(ItemConfig:GetSlgQuality(itemID))
        SetUIImage(border, borderIconPath, false)
        -- 图标
        local icon = GetChild(item, "icon", UEUI.Image)
        local iconPath = ItemConfig:GetIcon(itemID)
        SetUIImage(icon, iconPath, false)
        -- 数量
        local textNum = GetChild(item, "num", UEUI.Text)
        textNum.text = "x" .. NumToGameString(itemNum)
        -- 按钮
        local button = GetChild(item, "icon", UEUI.Button)
        button.onClick:RemoveAllListeners()
        button.onClick:AddListener(function ()
            UI_SHOW(UIDefine.UI_ItemTips, itemID)
        end)

        -- 被掠夺状态
        local mask = GetChild(item, "mask")
        local missState = value.status == TRADE_WAGONS_REWARD_STATUS.Lost
        SetActive(mask, missState)

        SetActive(item, true)
    end
end

--endregion -------------------------------------- 战斗记录 -----------------------------------------

return UI_TradeWagonsTrainDetail