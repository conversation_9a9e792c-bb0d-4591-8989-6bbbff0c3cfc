local UI_TradeWagonsViewModel = {}

UI_TradeWagonsViewModel.config = {["name"] = "UI_TradeWagonsView", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = false, ["anim"] = 0,["background"] = 0, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_TradeWagonsViewModel:Init(c)
    c.ui = {}    
    c.ui.m_imgBg = GetChild(c.uiGameObject,"m_imgBg",UEUI.Image)
    c.ui.m_imgBg2 = GetChild(c.uiGameObject,"m_imgBg2",UEUI.Image)
    c.ui.m_goTab = GetChild(c.uiGameObject,"bg/top/m_goTab")
    c.ui.m_btnTabOtherTruck = GetChild(c.uiGameObject,"bg/top/m_goTab/m_btnTabOtherTruck",UEUI.Button)
    c.ui.m_imgTabOtherTruckLight = GetChild(c.uiGameObject,"bg/top/m_goTab/m_btnTabOtherTruck/m_imgTabOtherTruckLight",UEUI.Image)
    c.ui.m_btnTabMyTruck = GetChild(c.uiGameObject,"bg/top/m_goTab/m_btnTabMyTruck",UEUI.Button)
    c.ui.m_imgTabMyTruckLight = GetChild(c.uiGameObject,"bg/top/m_goTab/m_btnTabMyTruck/m_imgTabMyTruckLight",UEUI.Image)
    c.ui.m_txtCountToday = GetChild(c.uiGameObject,"bg/top/bg/m_txtCountToday",UEUI.Text)
    c.ui.m_goPlunderRedPoint = GetChild(c.uiGameObject,"bg/top/bg/m_txtCountToday/m_goPlunderRedPoint")
    c.ui.m_btnHelp = GetChild(c.uiGameObject,"bg/top/m_btnHelp",UEUI.Button)
    c.ui.m_imgHelp = GetChild(c.uiGameObject,"bg/top/m_btnHelp/m_imgHelp",UEUI.Image)
    c.ui.m_goTruckOtherInfo = GetChild(c.uiGameObject,"bg/top/m_goTruckOtherInfo")
    c.ui.m_btnPlunderRecord = GetChild(c.uiGameObject,"bg/top/m_goTruckOtherInfo/btns/PlunderRecord/m_btnPlunderRecord",UEUI.Button)
    c.ui.m_btnResetWagon = GetChild(c.uiGameObject,"bg/top/m_goTruckOtherInfo/btns/ResetWagon/m_btnResetWagon",UEUI.Button)
    c.ui.m_btnStrongGift = GetChild(c.uiGameObject,"bg/top/m_btnStrongGift",UEUI.Button)
    c.ui.m_txtTimer = GetChild(c.uiGameObject,"bg/top/m_btnStrongGift/timer/m_txtTimer",UEUI.Text)
    c.ui.m_goMiddleOther = GetChild(c.uiGameObject,"bg/m_goMiddleOther")
    c.ui.m_transWagon = GetChild(c.uiGameObject,"bg/m_goMiddleOther/m_transWagon",UE.Transform)
    c.ui.m_goWagonItem = GetChild(c.uiGameObject,"bg/m_goMiddleOther/m_goWagonItem")
    c.ui.m_imgFitBg = GetChild(c.uiGameObject,"bg/m_goMiddleOther/mask/m_imgFitBg",UEUI.Image)
    c.ui.m_goTradeTrain = GetChild(c.uiGameObject,"bg/m_goMiddleOther/mask/m_imgFitBg/m_goTradeTrain")
    c.ui.m_goTradeTrain2 = GetChild(c.uiGameObject,"bg/m_goMiddleOther/mask/m_imgFitBg/m_goTradeTrain2")
    c.ui.m_goPlunderTrainEffect = GetChild(c.uiGameObject,"bg/m_goMiddleOther/mask/m_imgFitBg/m_goTradeTrain2/m_goPlunderTrainEffect")
    c.ui.m_btnStartShipping = GetChild(c.uiGameObject,"bg/m_goMiddleOther/StartShipping/m_btnStartShipping",UEUI.Button)
    c.ui.m_txtCountStart = GetChild(c.uiGameObject,"bg/m_goMiddleOther/StartShipping/m_btnStartShipping/m_txtCountStart",UEUI.Text)
    c.ui.m_goRedPoint = GetChild(c.uiGameObject,"bg/m_goMiddleOther/StartShipping/m_btnStartShipping/m_goRedPoint")
    c.ui.m_txtRedPointNum = GetChild(c.uiGameObject,"bg/m_goMiddleOther/StartShipping/m_btnStartShipping/m_goRedPoint/m_txtRedPointNum",UEUI.Text)
    c.ui.m_goGuide = GetChild(c.uiGameObject,"bg/m_goGuide")
    c.ui.m_goCloud = GetChild(c.uiGameObject,"m_goCloud")
    c.ui.m_goMask = GetChild(c.uiGameObject,"m_goMask")
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_TradeWagonsViewModel