{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 48860, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 48860, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 48860, "tid": 18226, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 48860, "tid": 18226, "ts": 1758262090367018, "dur": 412, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 48860, "tid": 18226, "ts": 1758262090369567, "dur": 424, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 48860, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 48860, "tid": 1, "ts": 1758262088770241, "dur": 3335, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 48860, "tid": 1, "ts": 1758262088773579, "dur": 33749, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 48860, "tid": 1, "ts": 1758262088807335, "dur": 22218, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 48860, "tid": 18226, "ts": 1758262090369993, "dur": 6, "ph": "X", "name": "", "args": {}}, {"pid": 48860, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088769005, "dur": 7870, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088776878, "dur": 1585264, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088777584, "dur": 2047, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088779635, "dur": 873, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088780510, "dur": 229, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088780744, "dur": 7, "ph": "X", "name": "ProcessMessages 20507", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088780753, "dur": 32, "ph": "X", "name": "ReadAsync 20507", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088780788, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088780790, "dur": 25, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088780817, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088780841, "dur": 489, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781333, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781419, "dur": 3, "ph": "X", "name": "ProcessMessages 10551", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781422, "dur": 63, "ph": "X", "name": "ReadAsync 10551", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781487, "dur": 16, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781504, "dur": 12, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781518, "dur": 12, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781532, "dur": 16, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781552, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781572, "dur": 23, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781597, "dur": 14, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781612, "dur": 13, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781627, "dur": 10, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781638, "dur": 10, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781650, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781663, "dur": 11, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781675, "dur": 11, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781688, "dur": 11, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781701, "dur": 13, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781715, "dur": 10, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781727, "dur": 9, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781737, "dur": 10, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781749, "dur": 13, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781763, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781764, "dur": 9, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781775, "dur": 11, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781787, "dur": 10, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781799, "dur": 11, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781811, "dur": 16, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781829, "dur": 8, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781838, "dur": 9, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781849, "dur": 23, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781874, "dur": 10, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781885, "dur": 10, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781897, "dur": 10, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781909, "dur": 11, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781921, "dur": 9, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781931, "dur": 10, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781944, "dur": 10, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781956, "dur": 8, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781966, "dur": 11, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781978, "dur": 16, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088781996, "dur": 13, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782011, "dur": 8, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782021, "dur": 9, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782032, "dur": 13, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782047, "dur": 11, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782060, "dur": 12, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782074, "dur": 11, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782087, "dur": 9, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782098, "dur": 29, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782130, "dur": 11, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782144, "dur": 14, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782159, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782184, "dur": 12, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782198, "dur": 9, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782209, "dur": 11, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782221, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782224, "dur": 14, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782239, "dur": 12, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782253, "dur": 12, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782267, "dur": 15, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782284, "dur": 11, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782297, "dur": 13, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782313, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782332, "dur": 12, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782347, "dur": 11, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782360, "dur": 12, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782374, "dur": 11, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782387, "dur": 12, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782402, "dur": 12, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782416, "dur": 11, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782429, "dur": 12, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782443, "dur": 11, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782457, "dur": 10, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782469, "dur": 14, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782486, "dur": 14, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782502, "dur": 11, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782514, "dur": 21, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782538, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782552, "dur": 14, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782568, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782570, "dur": 11, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782582, "dur": 9, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782593, "dur": 22, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782617, "dur": 26, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782645, "dur": 16, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782665, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782688, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782690, "dur": 17, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782709, "dur": 11, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782722, "dur": 13, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782737, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782753, "dur": 10, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782766, "dur": 9, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782776, "dur": 10, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782788, "dur": 13, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782804, "dur": 10, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782815, "dur": 14, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782830, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782832, "dur": 13, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782847, "dur": 12, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782861, "dur": 15, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782878, "dur": 11, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782891, "dur": 10, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782904, "dur": 11, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782917, "dur": 11, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782930, "dur": 35, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782968, "dur": 11, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782982, "dur": 13, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088782997, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783012, "dur": 11, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783027, "dur": 11, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783040, "dur": 11, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783054, "dur": 12, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783067, "dur": 10, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783079, "dur": 9, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783090, "dur": 12, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783106, "dur": 11, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783119, "dur": 11, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783132, "dur": 10, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783144, "dur": 11, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783159, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783183, "dur": 8, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783194, "dur": 9, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783205, "dur": 10, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783218, "dur": 11, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783231, "dur": 11, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783243, "dur": 11, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783256, "dur": 16, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783274, "dur": 8, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783284, "dur": 11, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783296, "dur": 9, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783308, "dur": 10, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783320, "dur": 11, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783333, "dur": 14, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783349, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783350, "dur": 11, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783363, "dur": 11, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783375, "dur": 13, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783390, "dur": 45, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783437, "dur": 11, "ph": "X", "name": "ReadAsync 1357", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783451, "dur": 12, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783465, "dur": 10, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783477, "dur": 12, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783491, "dur": 16, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783510, "dur": 14, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783525, "dur": 11, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783539, "dur": 14, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783555, "dur": 20, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783577, "dur": 8, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783587, "dur": 10, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783599, "dur": 14, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783615, "dur": 12, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783629, "dur": 32, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783663, "dur": 17, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783681, "dur": 12, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783696, "dur": 12, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783711, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783729, "dur": 12, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783743, "dur": 12, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783757, "dur": 28, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783787, "dur": 10, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783798, "dur": 10, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783811, "dur": 14, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783826, "dur": 18, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783847, "dur": 11, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783860, "dur": 11, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783873, "dur": 11, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783886, "dur": 14, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783902, "dur": 13, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088783917, "dur": 18109, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088802032, "dur": 2, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088802036, "dur": 707, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088802744, "dur": 7, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088802752, "dur": 57, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088802812, "dur": 3, "ph": "X", "name": "ProcessMessages 4767", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088802828, "dur": 115, "ph": "X", "name": "ReadAsync 4767", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088802946, "dur": 3, "ph": "X", "name": "ProcessMessages 7776", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088802950, "dur": 20, "ph": "X", "name": "ReadAsync 7776", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088802971, "dur": 1, "ph": "X", "name": "ProcessMessages 1452", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088802972, "dur": 10, "ph": "X", "name": "ReadAsync 1452", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088802984, "dur": 32, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803018, "dur": 21, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803042, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803055, "dur": 19, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803076, "dur": 10, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803087, "dur": 13, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803103, "dur": 10, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803115, "dur": 12, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803129, "dur": 9, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803140, "dur": 24, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803168, "dur": 14, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803184, "dur": 13, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803200, "dur": 12, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803214, "dur": 39, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803255, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803269, "dur": 10, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803282, "dur": 10, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803293, "dur": 10, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803305, "dur": 9, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803316, "dur": 15, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803333, "dur": 10, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803345, "dur": 11, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803357, "dur": 10, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803370, "dur": 14, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803385, "dur": 11, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803398, "dur": 10, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803409, "dur": 11, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803422, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803438, "dur": 12, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803452, "dur": 10, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803464, "dur": 16, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803481, "dur": 13, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803496, "dur": 11, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803508, "dur": 145, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803655, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803668, "dur": 12, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803682, "dur": 11, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803696, "dur": 12, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803710, "dur": 10, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803721, "dur": 10, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803733, "dur": 12, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803746, "dur": 15, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803763, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088803775, "dur": 367, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804147, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804184, "dur": 326, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804513, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804555, "dur": 2, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804558, "dur": 13, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804574, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804604, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804606, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804626, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804628, "dur": 10, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804641, "dur": 15, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804658, "dur": 11, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804671, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804687, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804706, "dur": 17, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804727, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804746, "dur": 11, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804759, "dur": 8, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804771, "dur": 17, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804790, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804792, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804812, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804828, "dur": 14, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804846, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804862, "dur": 10, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804874, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804898, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804899, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088804916, "dur": 122, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805040, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805042, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805069, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805071, "dur": 33, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805107, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805122, "dur": 10, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805135, "dur": 8, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805145, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805158, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805169, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805183, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805196, "dur": 11, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805211, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805226, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805243, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805259, "dur": 11, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805272, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805287, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805301, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805320, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805341, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805361, "dur": 10, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805374, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805387, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805398, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805413, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805427, "dur": 9, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805437, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805451, "dur": 10, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805464, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805478, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805496, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805499, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805521, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805534, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805589, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805605, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805618, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805640, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805652, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805710, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805731, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805766, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805784, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805795, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805911, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805931, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805960, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805973, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805985, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088805998, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088806045, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088806057, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088806080, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088806093, "dur": 318, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088806413, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088806426, "dur": 4342, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088810772, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088810775, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088810808, "dur": 326, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811137, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811139, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811167, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811169, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811193, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811243, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811267, "dur": 282, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811551, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811572, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811598, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811612, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811655, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811662, "dur": 179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811843, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811866, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811901, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088811919, "dur": 108, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812030, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812043, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812157, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812173, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812187, "dur": 8, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812196, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812199, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812214, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812245, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812256, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812273, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812284, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812354, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812367, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812416, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812427, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812450, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812465, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812479, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812551, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812561, "dur": 65, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812628, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812636, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812654, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812666, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812679, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812733, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812748, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812760, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812791, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812806, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812940, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812953, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812965, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812977, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088812996, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813006, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813018, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813030, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813041, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813070, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813081, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813240, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813251, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813282, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813297, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813318, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813331, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813342, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813376, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813385, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813401, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813414, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813470, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813487, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813497, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813513, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813529, "dur": 10, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813541, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813559, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813571, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813587, "dur": 14, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813603, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813617, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813633, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813643, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813659, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813669, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813679, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813693, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813702, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813721, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813722, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813738, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813769, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088813784, "dur": 233, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814020, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814032, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814042, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814055, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814072, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814087, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814100, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814112, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814138, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814151, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814167, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814181, "dur": 9, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814192, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814210, "dur": 10, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814224, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814234, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814253, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814266, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814295, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814305, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814321, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814346, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814359, "dur": 12, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814374, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814392, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814406, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814420, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814430, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814444, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814455, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814465, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814479, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814496, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814509, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814530, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814548, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814589, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814600, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814611, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814640, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814651, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814671, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814683, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814717, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814732, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814757, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814777, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814803, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814821, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814864, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814880, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814890, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814892, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814920, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814932, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814946, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814956, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814958, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088814976, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815024, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815045, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815074, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815094, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815106, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815142, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815157, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815174, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815182, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815202, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815252, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815264, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815305, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815319, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815361, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815378, "dur": 75, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815455, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815465, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815475, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815519, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815528, "dur": 120, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815649, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815665, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815699, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088815715, "dur": 353, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088816069, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088816081, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088816094, "dur": 1283, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088817379, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088817382, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088817394, "dur": 399, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262088817794, "dur": 1106155, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262089923964, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262089923967, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262089924018, "dur": 5581, "ph": "X", "name": "ProcessMessages 9959", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262089929602, "dur": 30, "ph": "X", "name": "ReadAsync 9959", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262089929635, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262089929637, "dur": 216, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262089929855, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262089929883, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262089929893, "dur": 27533, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262089957433, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262089957435, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262089957464, "dur": 398006, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262090355476, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262090355478, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262090355496, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262090355497, "dur": 1194, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262090356694, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262090356696, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262090356709, "dur": 12, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262090356722, "dur": 510, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262090357235, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262090357245, "dur": 244, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 48860, "tid": 12884901888, "ts": 1758262090357490, "dur": 4170, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 48860, "tid": 18226, "ts": 1758262090370000, "dur": 403, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 48860, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 48860, "tid": 8589934592, "ts": 1758262088767216, "dur": 62383, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 48860, "tid": 8589934592, "ts": 1758262088829602, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 48860, "tid": 8589934592, "ts": 1758262088829605, "dur": 960, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 48860, "tid": 18226, "ts": 1758262090370404, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 48860, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 48860, "tid": 4294967296, "ts": 1758262088752694, "dur": 1610247, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 48860, "tid": 4294967296, "ts": 1758262088756155, "dur": 6796, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 48860, "tid": 4294967296, "ts": 1758262090362952, "dur": 2578, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 48860, "tid": 4294967296, "ts": 1758262090364364, "dur": 68, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 48860, "tid": 4294967296, "ts": 1758262090365569, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 48860, "tid": 18226, "ts": 1758262090370411, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1758262088775203, "dur": 1169, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758262088776381, "dur": 695, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758262088777152, "dur": 679, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758262088778487, "dur": 1190, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_A38B4233660E9CB9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1758262088780411, "dur": 865, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1758262088781878, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1758262088785123, "dur": 17674, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1758262088803370, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1758262088777846, "dur": 26455, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758262088804308, "dur": 1553076, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758262090357491, "dur": 59, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758262090357762, "dur": 677, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1758262088777695, "dur": 26621, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088804341, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758262088804401, "dur": 203, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1758262088804334, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_D699E5DF035CDE66.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758262088804746, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_2D0561EE77024B7A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758262088805016, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Common.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758262088805015, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_2D9AC79B2BBA2E00.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758262088806397, "dur": 924, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Editor\\spine-unity\\Modules\\Timeline\\Editor\\SpineAnimationStateDrawer.cs"}}, {"pid": 12345, "tid": 1, "ts": 1758262088806038, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088807343, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088807685, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088808117, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Messaging\\UdpSocket.cs"}}, {"pid": 12345, "tid": 1, "ts": 1758262088807907, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088808663, "dur": 689, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\RiderScriptEditor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1758262088809623, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\ProjectGeneration\\GUIDProvider.cs"}}, {"pid": 12345, "tid": 1, "ts": 1758262088808642, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088810209, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088810896, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088811384, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088811696, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088812122, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088812589, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758262088812968, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758262088812701, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758262088813321, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088813503, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088813600, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088813878, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088814269, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088814703, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088814939, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088815036, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088815188, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088815293, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088815492, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088815581, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088815767, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088815878, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088815929, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088816206, "dur": 15823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262088832029, "dur": 1096400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262089928430, "dur": 29462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758262089957939, "dur": 399337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088777719, "dur": 26616, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088804346, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758262088804417, "dur": 425, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1758262088804341, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_65323E1310BDBE7B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758262088804855, "dur": 569, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_65323E1310BDBE7B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758262088805426, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088805992, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088806504, "dur": 751, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\CasualGame\\FlexyRing\\Script\\GameScript\\LevelController.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758262088806504, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088807438, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088807906, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088808115, "dur": 716, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Window\\Modes\\TimelineActiveMode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758262088808115, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088809572, "dur": 622, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\ConfirmContinueWithPendingChangesDialog.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758262088809218, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088810680, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088811445, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088811700, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088812120, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088812406, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088812567, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758262088813186, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758262088813551, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758262088813087, "dur": 763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758262088813850, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088814033, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758262088814526, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088814955, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088815175, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088815295, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088815490, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088815580, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088815777, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088815884, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088815935, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262088816220, "dur": 1112226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758262089928446, "dur": 428852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088777708, "dur": 26621, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088804344, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758262088804399, "dur": 399, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1758262088804335, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BD6B6DA2CAED0C51.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758262088804827, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BD6B6DA2CAED0C51.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758262088805055, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758262088805054, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_ED95F4E26287FEC9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758262088805159, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088805280, "dur": 274, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Compat.dll_BF1C3A400F471BD4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758262088805618, "dur": 812, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088806546, "dur": 935, "ph": "X", "name": "File", "args": {"detail": "Assets\\UnityWebSocket\\Demo\\UnityWebSocketDemo.cs"}}, {"pid": 12345, "tid": 3, "ts": 1758262088806438, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088807711, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088808349, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088808710, "dur": 1413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088810124, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088810553, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088810657, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088811337, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088811715, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088812137, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088812443, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758262088813283, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758262088813772, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088814084, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758262088814514, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088815011, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\WX-WASM-SDK-V2\\Runtime\\Plugins\\LitJson.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758262088814885, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758262088815572, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088815749, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088815873, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088815926, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262088816209, "dur": 1112222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758262089928432, "dur": 428869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088777744, "dur": 26606, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088804358, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1758262088804423, "dur": 468, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1758262088804357, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_D57CD8953DD5F64D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758262088804978, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088805446, "dur": 861, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1758262088806308, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088807158, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088807581, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088808026, "dur": 1121, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Utilities\\Editor\\Toolbar.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758262088807845, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088809300, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088810523, "dur": 841, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\double4x3.gen.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758262088810402, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088811713, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088812124, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088812448, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758262088813186, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 4, "ts": 1758262088813554, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\ObjectMenuCreation\\MenuItems.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758262088812692, "dur": 1226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758262088813919, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088814286, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088814925, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 4, "ts": 1758262088814685, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758262088815330, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088815469, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088815585, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088815765, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088815869, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088815923, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262088816213, "dur": 1112226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758262089928440, "dur": 428889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088777726, "dur": 26616, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088804352, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758262088804418, "dur": 406, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1758262088804349, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_A062D8221399A90F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758262088804845, "dur": 486, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_A062D8221399A90F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758262088805379, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088805519, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088806051, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088807036, "dur": 1218, "ph": "X", "name": "File", "args": {"detail": "Assets\\FlexReader\\Converter\\CustomConverters\\Vector3Converter.cs"}}, {"pid": 12345, "tid": 5, "ts": 1758262088806903, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088809610, "dur": 531, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Manipulators\\Sequence\\RectangleTool.cs"}}, {"pid": 12345, "tid": 5, "ts": 1758262088808333, "dur": 1910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088810541, "dur": 835, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 5, "ts": 1758262088810244, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088811435, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088811708, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088812130, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088812417, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088812798, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758262088812889, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758262088812980, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758262088813187, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088813555, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758262088813770, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088813867, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088814040, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088814258, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088814592, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088814702, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088814801, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088814937, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088815040, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088815198, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088815292, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088815468, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088815568, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088815744, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088815923, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262088816238, "dur": 1112200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758262089928438, "dur": 428990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758262088778095, "dur": 26405, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758262088804526, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1758262088804500, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_0D716A535EA5D9A0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758262088804761, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758262088805009, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758262088805009, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_A6E727B0B6898769.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758262088805078, "dur": 647, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_A6E727B0B6898769.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758262088805731, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758262088806276, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758262088806491, "dur": 476, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758262088807693, "dur": 453, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758262088808207, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758262088808290, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758262088808677, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758262088808784, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogAssert.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758262088809017, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnhandledLogMessageException.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758262088809173, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestEnumerator.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758262088809372, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityPlatformAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758262088809502, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnitySetUpAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758262088809589, "dur": 511, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTearDownAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758262088810149, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandBase.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758262088810310, "dur": 831, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRetryTestCommand.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758262088811142, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableSetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758262088811251, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\TestCommandPcHelper.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758262088805835, "dur": 5689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758262088811524, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758262088811769, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758262088812187, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758262088812439, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758262088812519, "dur": 960, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758262088813481, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758262088814223, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758262088814611, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758262088814926, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Sirenix\\Assemblies\\Sirenix.Serialization.Config.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758262088814884, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758262088815635, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758262088815918, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758262088815987, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758262088816203, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758262088817128, "dur": 86, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758262088817927, "dur": 1107009, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758262089928652, "dur": 29098, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758262089928440, "dur": 29403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758262089957922, "dur": 399509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088777772, "dur": 26600, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088804397, "dur": 457, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1758262088804376, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_59D59D2570B707D8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758262088804907, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088805713, "dur": 609, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1758262088806322, "dur": 520, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\DG_Tweening_DOTweenAnimationWrap.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758262088806322, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088807045, "dur": 1500, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Demigiant\\DOTweenPro\\DOTweenAnimation.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758262088807045, "dur": 2092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088809505, "dur": 786, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Merge\\Developer\\MergeViewMenu.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758262088809137, "dur": 1548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088810685, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088811185, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088811314, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088811705, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088812132, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088812439, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758262088812779, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1758262088812995, "dur": 293, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1758262088813296, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1758262088812694, "dur": 1256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758262088813951, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088814063, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088814268, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088814743, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088814796, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088814997, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088815202, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088815299, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088815465, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088815567, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088815753, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088815880, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088815930, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262088816214, "dur": 1112262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758262089928477, "dur": 428798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088777798, "dur": 26593, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088804411, "dur": 445, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1758262088804395, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7C2C4F34C6B423AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758262088804944, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088805398, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1758262088806050, "dur": 888, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088807040, "dur": 977, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-unity\\Modules\\YieldInstructions\\WaitForSpineAnimationComplete.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758262088808118, "dur": 754, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-unity\\Modules\\Timeline\\SpineSkeletonFlip\\SpineSkeletonFlipBehaviour.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758262088806946, "dur": 2038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088808984, "dur": 1573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088810587, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088810849, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088811320, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088811693, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088812116, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088812447, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758262088813483, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 8, "ts": 1758262088813868, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\UnitTesting\\CallbackInitializer.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758262088814040, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 8, "ts": 1758262088813322, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758262088814123, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088814276, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088814691, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088815044, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088815310, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088815473, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088815573, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088815762, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088815922, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262088816206, "dur": 1112271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758262089928478, "dur": 429036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088778167, "dur": 26295, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088804490, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1758262088804463, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_F085666F1110AAE3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758262088804743, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088805006, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758262088805005, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_78AB6F32676F7162.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758262088805070, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088805190, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088805421, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088805624, "dur": 688, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1758262088806313, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\Mosframe_TableViewWrap.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758262088806313, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088807040, "dur": 1122, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-csharp\\SkeletonBinary.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758262088806994, "dur": 1712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088808707, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088809425, "dur": 850, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\Tree\\TreeViewSessionState.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758262088809296, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088810789, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088811316, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088811707, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088812128, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088812446, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758262088813078, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088813296, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758262088813159, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758262088813978, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088814290, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088814600, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758262088815207, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088815260, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088815674, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088815757, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088815869, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088815924, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262088816222, "dur": 1112227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758262089928449, "dur": 428861, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088777853, "dur": 26570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088804440, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1758262088804427, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_24A8B6F9BAE245F7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758262088804680, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088804731, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_FD63888855540244.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758262088804906, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088805254, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088805689, "dur": 880, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088806817, "dur": 791, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Proto\\Fight.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758262088806574, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088807962, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088809557, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088810756, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088811255, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088811309, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088811694, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088812118, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088812404, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088812469, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758262088812778, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 10, "ts": 1758262088812996, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1758262088813296, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1758262088813551, "dur": 290, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\iOSSupport\\UnityEditor.iOS.Extensions.Xcode.dll"}}, {"pid": 12345, "tid": 10, "ts": 1758262088813868, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 10, "ts": 1758262088814040, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1758262088814549, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Testing\\TestStatusAdaptor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758262088812723, "dur": 1953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758262088814676, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088814846, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088814939, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088815022, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088815175, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088815292, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088815477, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088815605, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088815748, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088815917, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758262088816056, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262088816211, "dur": 1112242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758262089928454, "dur": 428845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088777874, "dur": 26554, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088804447, "dur": 260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1758262088804435, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_3D07E2B87C74DF17.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758262088804759, "dur": 536, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_6BFC5EB2F5E95ABF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758262088805383, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088805603, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1758262088806490, "dur": 1836, "ph": "X", "name": "File", "args": {"detail": "Assets\\IronSource\\Editor\\IronSourceMediatedNetworkSettings.cs"}}, {"pid": 12345, "tid": 11, "ts": 1758262088806032, "dur": 2346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088809186, "dur": 914, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Activation\\ActivationTrackInspector.cs"}}, {"pid": 12345, "tid": 11, "ts": 1758262088808378, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088810158, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088810903, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088811328, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088811708, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088812127, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088812441, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758262088813186, "dur": 309, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758262088813551, "dur": 316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758262088813868, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758262088814040, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758262088813086, "dur": 1317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1758262088814404, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088814702, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758262088814879, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1758262088815481, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088815582, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088815760, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088815927, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262088816216, "dur": 1112237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758262089928453, "dur": 428852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088777894, "dur": 26540, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088804456, "dur": 256, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1758262088804438, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8D0CB068D769A31A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758262088804736, "dur": 676, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8D0CB068D769A31A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758262088805493, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1758262088805773, "dur": 680, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088806819, "dur": 884, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\ScriptDLL\\InjectiondoubleArray.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758262088806460, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088807992, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Assets\\AppleSignIn\\AppleAuth\\Interfaces\\IPersonName.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758262088807849, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088809545, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Attributes\\TrackColorAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758262088808997, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088810219, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088811116, "dur": 105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088811221, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088811313, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088811695, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088812115, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088812403, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088812585, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758262088812756, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088813187, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1758262088813483, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 12, "ts": 1758262088813008, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1758262088813658, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088814260, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1758262088814873, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088815005, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088815183, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088815287, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088815379, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088815494, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088815583, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088815745, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088815865, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088815920, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262088816207, "dur": 1112219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758262089928429, "dur": 427535, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1758262089928428, "dur": 427537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1758262090355981, "dur": 1235, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1758262088777911, "dur": 26529, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088804481, "dur": 396, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1758262088804470, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1542A8759536CB9C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1758262088804950, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088805085, "dur": 458, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_389506176DEC1286.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1758262088805561, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1758262088805621, "dur": 698, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1758262088806497, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\GameHelperWrap.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758262088806320, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088808124, "dur": 1049, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Linq\\Where.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758262088807208, "dur": 1998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088809621, "dur": 677, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Diff\\Dialogs\\GetRestorePathDialog.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758262088809206, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088810437, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088810807, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088811419, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088811717, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088812121, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088812409, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088812588, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1758262088812777, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1758262088813055, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088813185, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088813861, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088814273, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088814708, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088814934, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\IngameDebugConsole.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1758262088814933, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/IngameDebugConsole.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1758262088815018, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088815179, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088815291, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088815480, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088815577, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088815769, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088815926, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262088816203, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1758262088816622, "dur": 1111821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758262089928443, "dur": 428933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088777934, "dur": 26513, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088804459, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1758262088804448, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_7B26F11653A8BB46.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758262088804669, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088804737, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_CA117DF3E8BEFDF6.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758262088805014, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088805072, "dur": 473, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_21675FD99AD58191.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758262088805569, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1758262088805983, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088806521, "dur": 847, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\CasualGame\\DLYShuiGuan\\Script\\DLYShuiGuanEnter.cs"}}, {"pid": 12345, "tid": 14, "ts": 1758262088806521, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088807830, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088808452, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088809529, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088810722, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088811250, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088811309, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088811697, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088812118, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088812401, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088812588, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758262088812780, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DemiLib\\DemiLib.dll"}}, {"pid": 12345, "tid": 14, "ts": 1758262088812995, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\FacebookSDK\\Plugins\\Gameroom\\Facebook.Unity.Gameroom.dll"}}, {"pid": 12345, "tid": 14, "ts": 1758262088813295, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Sirenix\\Assemblies\\Sirenix.OdinInspector.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1758262088813482, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1758262088812779, "dur": 1108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1758262088814247, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 14, "ts": 1758262088814425, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 14, "ts": 1758262088814679, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\AppleAuth.ref.dll"}}, {"pid": 12345, "tid": 14, "ts": 1758262088813939, "dur": 811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1758262088814751, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088815057, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088815171, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088815293, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088815480, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088815568, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088815763, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088815920, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758262088816053, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262088816217, "dur": 1112227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758262089928444, "dur": 428909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088777953, "dur": 26557, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088804523, "dur": 261, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1758262088804510, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_A31099604CCBA22C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1758262088804786, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088804861, "dur": 597, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_A31099604CCBA22C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1758262088805523, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088805656, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1758262088805706, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088805788, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088806817, "dur": 858, "ph": "X", "name": "File", "args": {"detail": "Assets\\Editor\\CustomTools\\TableViewDynamicEditor\\TableViewDynamicEditor.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758262088806035, "dur": 1713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088807748, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088809583, "dur": 698, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\TimelineAsset_CreateRemove.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758262088808865, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088810546, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088810837, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088811332, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088811709, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088812129, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088812404, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088812584, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1758262088813271, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1758262088813203, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1758262088813767, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088814278, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088814684, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1758262088815270, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088815341, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088815491, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088815580, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088815769, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088815879, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088815933, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262088816225, "dur": 1112211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758262089928437, "dur": 428941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088777968, "dur": 26535, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088804517, "dur": 256, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1758262088804504, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_7B54AD0B13F0D210.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758262088804784, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_7B54AD0B13F0D210.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758262088805937, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088806510, "dur": 816, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\CasualGame\\DLYShuiGuan\\Script\\Static\\FunctionLibrary.cs"}}, {"pid": 12345, "tid": 16, "ts": 1758262088806510, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088807509, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088808228, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088809338, "dur": 735, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Branch\\BranchesTab_Operations.cs"}}, {"pid": 12345, "tid": 16, "ts": 1758262088809250, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088810474, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088811213, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088811320, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088811703, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088812132, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088812418, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088812787, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758262088813339, "dur": 509, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088813863, "dur": 394, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DOTween\\Editor\\DOTweenEditor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1758262088814324, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\UnityChannel\\UnityStore.dll"}}, {"pid": 12345, "tid": 16, "ts": 1758262088813853, "dur": 977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1758262088814830, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088815006, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088815231, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088815298, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088815487, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088815584, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088815754, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088815936, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262088816212, "dur": 1112216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758262089928430, "dur": 538, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1758262089928429, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1758262089928996, "dur": 1420, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1758262089930419, "dur": 426911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088777986, "dur": 26516, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088804517, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1758262088804503, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_C549C74F0FF91F05.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1758262088804768, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088804831, "dur": 390, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_C549C74F0FF91F05.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1758262088805437, "dur": 564, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088806047, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088806819, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088807142, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088807552, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088807757, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088808186, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088809511, "dur": 664, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\PendingChanges\\Changelists\\ChangelistMenu.cs"}}, {"pid": 12345, "tid": 17, "ts": 1758262088809127, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088810231, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088811431, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088811716, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088812138, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088812417, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088812796, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1758262088812968, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1758262088813186, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1758262088813295, "dur": 265, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 17, "ts": 1758262088812889, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758262088813644, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088814011, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1758262088813906, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758262088814514, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088814762, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088814938, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088815025, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088815224, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088815295, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088815482, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088815575, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088815746, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088815933, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1758262088815999, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262088816213, "dur": 1112258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758262089928471, "dur": 428826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088777997, "dur": 26498, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088804524, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1758262088804496, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F61FF965A6F6FAF4.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758262088804800, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F61FF965A6F6FAF4.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758262088805031, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_919C6EC381C92D4C.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758262088805206, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088805529, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088805994, "dur": 492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088806488, "dur": 2697, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\CommonTools\\ScrollRectItem.cs"}}, {"pid": 12345, "tid": 18, "ts": 1758262088809573, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Common\\CSVReader.cs"}}, {"pid": 12345, "tid": 18, "ts": 1758262088806488, "dur": 3759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088810505, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int4x3.gen.cs"}}, {"pid": 12345, "tid": 18, "ts": 1758262088810247, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088811491, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088811721, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088812131, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088812405, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088812534, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758262088812644, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088812804, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1758262088813169, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088814011, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 18, "ts": 1758262088814272, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 18, "ts": 1758262088813813, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1758262088814564, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088814679, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Sirenix\\Assemblies\\Sirenix.Utilities.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1758262088814635, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1758262088815434, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088815486, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088815569, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088815747, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088815872, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088815929, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262088816212, "dur": 1112254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758262089928466, "dur": 428839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088778029, "dur": 26472, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088804521, "dur": 256, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1758262088804501, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_243621AF56FD8BB7.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758262088804808, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_243621AF56FD8BB7.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758262088805039, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758262088805038, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_036EBEF6E01DAAEF.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758262088805275, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_036EBEF6E01DAAEF.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758262088805555, "dur": 591, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088806148, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088806567, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088807150, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088807402, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088807671, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088808334, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088809475, "dur": 674, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\Avatar\\GetAvatar.cs"}}, {"pid": 12345, "tid": 19, "ts": 1758262088809463, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088810593, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088811035, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088811315, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088811703, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088812114, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088812439, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758262088812543, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088812708, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758262088812968, "dur": 228, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758262088812795, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758262088813471, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088813552, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758262088814011, "dur": 325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DOTweenPro\\DOTweenPro.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758262088814924, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMPro_TexturePostProcessor.cs"}}, {"pid": 12345, "tid": 19, "ts": 1758262088814007, "dur": 1076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758262088815144, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088815313, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088815487, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088815584, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088815759, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088815934, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262088816213, "dur": 1112245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758262089928459, "dur": 428823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088778048, "dur": 26436, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088804498, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": 1758262088804485, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_389F212FAF489129.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758262088804750, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088804807, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_389F212FAF489129.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758262088805035, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758262088805035, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_5733EFC1F4B68777.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758262088805205, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088805323, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088805527, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088805797, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088806059, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088806818, "dur": 936, "ph": "X", "name": "File", "args": {"detail": "Assets\\IronSource\\Scripts\\IUnityRewardedVideo.cs"}}, {"pid": 12345, "tid": 20, "ts": 1758262088806818, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088808233, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088808629, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088809614, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Activation\\ActivationPlayableAsset.cs"}}, {"pid": 12345, "tid": 20, "ts": 1758262088809046, "dur": 1989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088811064, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088811204, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088811322, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088811695, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088812132, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088812443, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758262088812548, "dur": 974, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088813834, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\AsyncLazy.cs"}}, {"pid": 12345, "tid": 20, "ts": 1758262088814012, "dur": 270, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\UnityAsyncExtensions.uGUI.cs"}}, {"pid": 12345, "tid": 20, "ts": 1758262088813525, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758262088814311, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088814679, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758262088814600, "dur": 852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758262088815453, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088815757, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088815935, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262088816226, "dur": 1112225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758262089928452, "dur": 428841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088778062, "dur": 26405, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088804493, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 21, "ts": 1758262088804468, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F5D6C7D883717C04.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758262088804775, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_DF3174D424654B67.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758262088805057, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 21, "ts": 1758262088805014, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_875FEDF767AE9596.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758262088805346, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088805467, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088805739, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088806125, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088806561, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088806733, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088807191, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088807400, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088808373, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088809493, "dur": 714, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Merge\\Developer\\DirectoryConflicts\\AddMoveMenu.cs"}}, {"pid": 12345, "tid": 21, "ts": 1758262088809139, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088810600, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088811302, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088811704, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088812130, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088812590, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758262088812704, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758262088813247, "dur": 934, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088814185, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088814582, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088814710, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088814939, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088815469, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088815574, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088815745, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088815921, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262088816220, "dur": 1112235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758262089928455, "dur": 428837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088778082, "dur": 26382, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088804480, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 22, "ts": 1758262088804465, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_7F3AF681D6CECB6F.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1758262088804667, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088804772, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_03C0A16AC7611605.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1758262088805009, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088805065, "dur": 468, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_52C8AB7AD3D8A783.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1758262088805538, "dur": 703, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088806260, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088806488, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088807044, "dur": 1162, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-unity\\Modules\\SkeletonGraphic\\SkeletonGraphicMirror.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758262088806962, "dur": 1774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088808737, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088809587, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088810811, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088811344, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088811721, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088812133, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088812440, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1758262088812578, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Sirenix\\Assemblies\\Sirenix.OdinInspector.Attributes.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758262088812778, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758262088813186, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758262088812558, "dur": 913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1758262088813471, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088814079, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1758262088814266, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DOTween\\DOTween43.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758262088814680, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758262088814261, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1758262088815162, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088815265, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088815496, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088815576, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088815781, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088815881, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088815932, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262088816220, "dur": 1112228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758262089928449, "dur": 428847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088777757, "dur": 26602, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088804415, "dur": 393, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 23, "ts": 1758262088804368, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_7D6B8E0347C20661.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1758262088804819, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_7D6B8E0347C20661.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1758262088805050, "dur": 203, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1758262088805050, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_90AA0436019DA739.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1758262088805566, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1758262088805693, "dur": 916, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088806821, "dur": 917, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Framework\\WebSocket\\WebTCPSession.cs"}}, {"pid": 12345, "tid": 23, "ts": 1758262088806613, "dur": 1627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088808241, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088809510, "dur": 591, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\PendingChanges\\UnityPendingChangesTree.cs"}}, {"pid": 12345, "tid": 23, "ts": 1758262088809046, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088810602, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088810980, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088811413, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088811714, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088812123, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088812445, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1758262088812777, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1758262088813478, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\AssetsUtils\\RepaintInspector.cs"}}, {"pid": 12345, "tid": 23, "ts": 1758262088814040, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\Tree\\TreeHeaderSettings.cs"}}, {"pid": 12345, "tid": 23, "ts": 1758262088812730, "dur": 1932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1758262088814663, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088814753, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088814881, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1758262088815119, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1758262088815578, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088815743, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088815866, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088815925, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262088816208, "dur": 1112250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758262089928458, "dur": 428835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088778121, "dur": 26369, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088804504, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 24, "ts": 1758262088804490, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FEB44C0922F521E3.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1758262088804787, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FEB44C0922F521E3.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1758262088805016, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_58061DC3ABA3501B.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1758262088805074, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088805429, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088805731, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1758262088806275, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 24, "ts": 1758262088806428, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 24, "ts": 1758262088806707, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 24, "ts": 1758262088806840, "dur": 955, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 24, "ts": 1758262088807846, "dur": 311, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 24, "ts": 1758262088808449, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 24, "ts": 1758262088808796, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\TouchInputModule.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758262088809078, "dur": 366, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\MoveDirection.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758262088809569, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758262088809640, "dur": 446, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Animation\\CoroutineTween.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758262088810115, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\CanvasUpdateRegistry.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758262088810406, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Graphic.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758262088810623, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\CanvasScaler.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758262088810700, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\GridLayoutGroup.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758262088810824, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutElement.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758262088810977, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\StencilMaterial.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758262088805800, "dur": 5427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1758262088811308, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088811696, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088812129, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088812402, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088812563, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1758262088812947, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1758262088813081, "dur": 1020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088814228, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088814285, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088814783, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088814945, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088815036, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088815122, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088815207, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088815298, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088815476, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088815585, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088815751, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088815868, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088815930, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262088816219, "dur": 1112246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758262089928465, "dur": 428873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088778143, "dur": 26343, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088804498, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 25, "ts": 1758262088804486, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_58980D046BFD513D.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1758262088804754, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088804830, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_58980D046BFD513D.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1758262088805194, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088805697, "dur": 740, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088806837, "dur": 1119, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\ScriptDLL\\InjectionText.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758262088806439, "dur": 1676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088808115, "dur": 643, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\PropertyCollector.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758262088808816, "dur": 585, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\ObjectExtension.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758262088809402, "dur": 757, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\MarkerModifier.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758262088808115, "dur": 2838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088810953, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088811353, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088811713, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088812125, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088812410, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088812788, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1758262088813262, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088814079, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1758262088814272, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 25, "ts": 1758262088814679, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 25, "ts": 1758262088814185, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1758262088814920, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088815019, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088815231, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088815294, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088815483, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088815571, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088815747, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088815928, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088816205, "dur": 14889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088831096, "dur": 927, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262088832023, "dur": 1096441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758262089928464, "dur": 428876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088777835, "dur": 26561, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088804422, "dur": 415, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 26, "ts": 1758262088804400, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_BDC059FEE0C56713.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1758262088804854, "dur": 634, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_BDC059FEE0C56713.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1758262088805489, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1758262088805560, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1758262088805736, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088806122, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088806250, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088806586, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088806749, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088807041, "dur": 2304, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-csharp\\Attachments\\PathAttachment.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758262088807016, "dur": 3365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088810818, "dur": 583, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\Geometry\\Plane.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758262088810382, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088811588, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088811702, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088812119, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088812415, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088812659, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1758262088812738, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1758262088812825, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088812995, "dur": 1025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088814071, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088814291, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088814724, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088814939, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088815037, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088815218, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088815297, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088815478, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088815577, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088815755, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088815927, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088816205, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262088816620, "dur": 1111853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758262089928474, "dur": 428815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088778182, "dur": 26286, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088804472, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1758262088804524, "dur": 319, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 27, "ts": 1758262088804469, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_FFCA22B00416E73E.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1758262088804855, "dur": 583, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_FFCA22B00416E73E.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1758262088805472, "dur": 537, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088806049, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088806910, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088807228, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088807501, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088807997, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088808637, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088809361, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088810637, "dur": 744, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool3.gen.cs"}}, {"pid": 12345, "tid": 27, "ts": 1758262088810435, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088811594, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088811702, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088812126, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088812446, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1758262088812545, "dur": 1387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088814011, "dur": 296, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1758262088813936, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758262088814677, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088814882, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1758262088815058, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758262088815785, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088815871, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088815925, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262088816209, "dur": 1112257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758262089928466, "dur": 428835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088778197, "dur": 26255, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088804464, "dur": 202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 28, "ts": 1758262088804452, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_4FFE5013DEBA169E.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758262088804668, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088804786, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_4FFE5013DEBA169E.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758262088805062, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1758262088805061, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_328CD27C104796DE.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758262088805389, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088805537, "dur": 628, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088806168, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088806279, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088806685, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088807032, "dur": 950, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-unity\\Asset Types\\SpineAtlasAsset.cs"}}, {"pid": 12345, "tid": 28, "ts": 1758262088806982, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088808092, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088808417, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088809379, "dur": 721, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Utilities\\IPropertyPreview.cs"}}, {"pid": 12345, "tid": 28, "ts": 1758262088808802, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088810101, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088810784, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088811263, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088811319, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088811702, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088812117, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088812402, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088812586, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758262088812674, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088812969, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1758262088812802, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758262088813435, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088814060, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758262088814512, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088814878, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088815045, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088815181, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088815294, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088815491, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088815566, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088815787, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088815921, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262088816215, "dur": 1112229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758262089928445, "dur": 428855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758262090360084, "dur": 1673, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 48860, "tid": 18226, "ts": 1758262090370694, "dur": 1441, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 48860, "tid": 18226, "ts": 1758262090372163, "dur": 596, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 48860, "tid": 18226, "ts": 1758262090368786, "dur": 4489, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}