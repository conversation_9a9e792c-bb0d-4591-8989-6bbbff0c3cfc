Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.62f1c1 (b0109b07edb8) revision 11538587'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 32596 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program\Unity\Editor\2022.3.62f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Project_Merge_Minigame/Client/Project_PT
-logFile
Logs/AssetImportWorker0.log
-srvPort
6763
Successfully changed project path to: D:/Project_Merge_Minigame/Client/Project_PT
D:/Project_Merge_Minigame/Client/Project_PT
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [44932]  Target information:

Player connection [44932]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 587791853 [EditorId] 587791853 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-ORSLCSS) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [44932] Host joined multi-casting on [***********:54997]...
Player connection [44932] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
Refreshing native plugins compatible for Editor in 18.43 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.62f1c1 (b0109b07edb8)
[Subsystems] Discovering subsystems at path D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Project_Merge_Minigame/Client/Project_PT/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 (ID=0x2808)
    Vendor:   NVIDIA
    VRAM:     7957 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56312
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: D:/Program/Unity/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002363 seconds.
- Loaded All Assemblies, in  0.181 seconds
Native extension for WindowsStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 2142 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.300 seconds
Domain Reload Profiling: 2481ms
	BeginReloadAssembly (51ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (77ms)
		LoadAssemblies (51ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (76ms)
			TypeCache.Refresh (75ms)
				TypeCache.ScanAssembly (68ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (2300ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2276ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2187ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (63ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.373 seconds
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.500 seconds
Domain Reload Profiling: 1874ms
	BeginReloadAssembly (77ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (13ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (5ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (249ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (110ms)
			TypeCache.Refresh (96ms)
				TypeCache.ScanAssembly (84ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (1501ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1414ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (27ms)
			ProcessInitializeOnLoadAttributes (1342ms)
			ProcessInitializeOnLoadMethodAttributes (29ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=83f58e5870b13bb4f81871734f194e1f): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 9.76 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4536 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1888 unused Assets / (6.4 MB). Loaded Objects now: 3908.
Memory consumption went from 0.85 GB to 0.85 GB.
Total: 18.219000 ms (FindLiveObjects: 0.260600 ms CreateObjectMapping: 0.120400 ms MarkObjects: 13.596200 ms  DeleteObjects: 4.240600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 80175.133453 seconds.
  path: Assets/ResPackage/Sprite/ui_activity_buff
  artifactKey: Guid(be959b6a2130c984ca2de1220248a60d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ResPackage/Sprite/ui_activity_buff using Guid(be959b6a2130c984ca2de1220248a60d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '63b3d9be60f22f36b3e96b076b84edb0') in 0.002781 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.272 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.504 seconds
Domain Reload Profiling: 1776ms
	BeginReloadAssembly (93ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (136ms)
		LoadAssemblies (168ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1504ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1255ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (25ms)
			ProcessInitializeOnLoadAttributes (1187ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=53a1a15a83cfd3c4e9738f6752875ca4): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 16.16 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Release of invalid GC handle. The handle is from a previous domain. The release operation is skipped.
Unloading 4236 Unused Serialized files (Serialized files now loaded: 0)
Release of invalid GC handle native callstack: 
0x00007ff727b2ff2d (Unity) StackWalker::GetCurrentCallstack
0x00007ff727b35009 (Unity) StackWalker::ShowCallstack
0x00007ff728af3ec1 (Unity) GetStacktrace
0x00007ff727a2b01d (Unity) ScriptingGCHandle::ReleaseAndClear
0x00007ff727fec8bc (Unity) UploadHandlerRaw::`vector deleting destructor'
0x00007ff727fed46d (Unity) UploadHandler::Release
0x00007ff727fe9d0d (Unity) UnityWebRequestProto<UnityWebRequestTransport,AtomicRefCounter,RedirectHelper,ResponseHelper,DownloadHandler,UploadHandler,CertificateHandler,HeaderHelper,AsyncOperation>::~UnityWebRequestProto<UnityWebRequestTransport,AtomicRefCounter,RedirectHelper,ResponseHelper,DownloadHandler,UploadHandler,CertificateHandler,HeaderHelper,AsyncOperation>
0x00007ff727fea006 (Unity) UnityWebRequest::`vector deleting destructor'
0x00007ff726ce2627 (Unity) UnityWebRequestProto<UnityWebRequestTransport,AtomicRefCounter,RedirectHelper,ResponseHelper,DownloadHandler,UploadHandler,CertificateHandler,HeaderHelper,AsyncOperation>::Release
0x00007ff727feba35 (Unity) UnityWebRequestAsyncOperation::InvokeCoroutine
0x00007ff727febc11 (Unity) UnityWebRequestProto<UnityWebRequestTransport,AtomicRefCounter,RedirectHelper,ResponseHelper,DownloadHandler,UploadHandler,CertificateHandler,HeaderHelper,AsyncOperation>::Job_InvokeCoroutine
0x00007ff7276596ea (Unity) BackgroundJobQueue::ExecuteMainThreadJobs
0x00007ff72770e43f (Unity) PreloadManager::WaitForAllAsyncOperationsToComplete
0x00007ff728e93996 (Unity) <lambda_a8cef15c13bd9ac3f7eff79c3cefd4ad>::operator()
0x00007ff728eceba2 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff728ebab4e (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff728ebc7b4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff728eccf4c (Unity) IOService::Run
0x00007ff728ea038f (Unity) AssetImportWorkerClient::Run
0x00007ff728e6bb77 (Unity) RunAssetImportWorkerClientV2
0x00007ff728e6bbfb (Unity) RunAssetImporterV2
0x00007ff7286835ee (Unity) Application::InitializeProject
0x00007ff728afeb75 (Unity) WinMain
0x00007ff729ec214e (Unity) __scrt_common_main_seh
0x00007ffebc44e8d7 (KERNEL32) BaseThreadInitThunk
0x00007ffebcea8d9c (ntdll) RtlUserThreadStart
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3911.
Memory consumption went from 0.79 GB to 0.78 GB.
Total: 18.253200 ms (FindLiveObjects: 0.268700 ms CreateObjectMapping: 0.113700 ms MarkObjects: 12.891600 ms  DeleteObjects: 4.978400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.280 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.528 seconds
Domain Reload Profiling: 1808ms
	BeginReloadAssembly (93ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (143ms)
		LoadAssemblies (177ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1528ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1289ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (26ms)
			ProcessInitializeOnLoadAttributes (1222ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=9d9591164bf5ef74e9d1f689cdc300f5): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 11.29 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4236 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3914.
Memory consumption went from 0.79 GB to 0.78 GB.
Total: 17.817500 ms (FindLiveObjects: 0.248700 ms CreateObjectMapping: 0.080200 ms MarkObjects: 12.796900 ms  DeleteObjects: 4.690400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.278 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.698 seconds
Domain Reload Profiling: 1975ms
	BeginReloadAssembly (94ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (142ms)
		LoadAssemblies (177ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1698ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1435ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (27ms)
			ProcessInitializeOnLoadAttributes (1365ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=8470b38b2d311664f8839d31d352c8b9): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 15.92 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4236 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3917.
Memory consumption went from 0.79 GB to 0.78 GB.
Total: 28.310600 ms (FindLiveObjects: 0.256800 ms CreateObjectMapping: 0.082500 ms MarkObjects: 19.668600 ms  DeleteObjects: 8.301300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.272 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.552 seconds
Domain Reload Profiling: 1824ms
	BeginReloadAssembly (92ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (136ms)
		LoadAssemblies (167ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1552ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1295ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (27ms)
			ProcessInitializeOnLoadAttributes (1224ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=d17379c57abba9741885364dac1d3050): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 8.27 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4236 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3920.
Memory consumption went from 0.79 GB to 0.78 GB.
Total: 16.437300 ms (FindLiveObjects: 0.209900 ms CreateObjectMapping: 0.068200 ms MarkObjects: 12.046500 ms  DeleteObjects: 4.111600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.264 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.539 seconds
Domain Reload Profiling: 1803ms
	BeginReloadAssembly (89ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (133ms)
		LoadAssemblies (163ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1539ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1299ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (26ms)
			ProcessInitializeOnLoadAttributes (1232ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=2abc9e48f1c764942a205c9423a52864): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 8.10 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4236 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3923.
Memory consumption went from 0.79 GB to 0.78 GB.
Total: 16.239600 ms (FindLiveObjects: 0.219000 ms CreateObjectMapping: 0.070800 ms MarkObjects: 11.768100 ms  DeleteObjects: 4.180500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.278 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.540 seconds
Domain Reload Profiling: 1818ms
	BeginReloadAssembly (92ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (143ms)
		LoadAssemblies (176ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1541ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1302ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (27ms)
			ProcessInitializeOnLoadAttributes (1233ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=a31ba4147b675cc4d99f89ea71277b1f): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 8.01 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4236 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3926.
Memory consumption went from 0.79 GB to 0.78 GB.
Total: 70.843100 ms (FindLiveObjects: 0.214500 ms CreateObjectMapping: 0.071100 ms MarkObjects: 11.914600 ms  DeleteObjects: 58.641700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.265 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Unable to write to 'ProjectSettings\GvhProjectSettings.xml' (System.IO.IOException: Sharing violation on path D:\Project_Merge_Minigame\Client\Project_PT\ProjectSettings\GvhProjectSettings.xml
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.IO.FileOptions options) [0x00000] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean useAsync) [0x00000] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int,bool)
  at System.Xml.XmlWriterSettings.CreateWriter (System.String outputFileName) [0x00051] in <cc6807978d504e8ea81a34b1b2ec871c>:0 
  at System.Xml.XmlWriter.Create (System.String outputFileName, System.Xml.XmlWriterSettings settings) [0x0000a] in <cc6807978d504e8ea81a34b1b2ec871c>:0 
  at Google.ProjectSettings.Save () [0x0006d] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:821 , Project settings were not saved!
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
Google.Logger:Log (string,Google.LogLevel) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/Logger.cs:136)
Google.ProjectSettings:Save () (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:844)
Google.ProjectSettings:SetBool (string,bool,Google.SettingsLocation) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:485)
Google.ProjectSettings:SetBool (string,bool) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:497)
Google.IOSResolver:set_VerboseLoggingEnabled (bool) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/IOSResolver/src/IOSResolver.cs:998)
Google.IOSResolver:.cctor () (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/IOSResolver/src/IOSResolver.cs:692)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs Line: 844)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.549 seconds
Domain Reload Profiling: 1814ms
	BeginReloadAssembly (93ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (130ms)
		LoadAssemblies (159ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1549ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1318ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (27ms)
			ProcessInitializeOnLoadAttributes (1249ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=0b931589a42b0bf4490618cd2f25ad14): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 8.39 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4236 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3929.
Memory consumption went from 0.79 GB to 0.78 GB.
Total: 16.906600 ms (FindLiveObjects: 0.245600 ms CreateObjectMapping: 0.078600 ms MarkObjects: 12.107000 ms  DeleteObjects: 4.474300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.276 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.521 seconds
Domain Reload Profiling: 1798ms
	BeginReloadAssembly (91ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (144ms)
		LoadAssemblies (174ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1522ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1280ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (27ms)
			ProcessInitializeOnLoadAttributes (1211ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=8872b3d36527b054a8a329ca19a4ad8d): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 9.43 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4236 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3932.
Memory consumption went from 0.79 GB to 0.78 GB.
Total: 17.474400 ms (FindLiveObjects: 0.234500 ms CreateObjectMapping: 0.077600 ms MarkObjects: 12.474800 ms  DeleteObjects: 4.686400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.257 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.498 seconds
Domain Reload Profiling: 1754ms
	BeginReloadAssembly (87ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (128ms)
		LoadAssemblies (157ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1498ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1257ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (26ms)
			ProcessInitializeOnLoadAttributes (1190ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=f124c94ff44dcbc4791a9d0cae5afdf4): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 10.39 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4236 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.4 MB). Loaded Objects now: 3935.
Memory consumption went from 0.79 GB to 0.78 GB.
Total: 82.619100 ms (FindLiveObjects: 0.245000 ms CreateObjectMapping: 0.081600 ms MarkObjects: 14.581200 ms  DeleteObjects: 67.710100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.278 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.516 seconds
Domain Reload Profiling: 1794ms
	BeginReloadAssembly (93ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (142ms)
		LoadAssemblies (174ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1517ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1282ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (27ms)
			ProcessInitializeOnLoadAttributes (1213ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=57d92cdaedf37674292393036d0a29f3): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 8.35 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4236 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3938.
Memory consumption went from 0.79 GB to 0.78 GB.
Total: 17.787100 ms (FindLiveObjects: 0.281800 ms CreateObjectMapping: 0.114500 ms MarkObjects: 12.714300 ms  DeleteObjects: 4.675300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.262 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Unable to write to 'ProjectSettings\GvhProjectSettings.xml' (System.IO.IOException: Sharing violation on path D:\Project_Merge_Minigame\Client\Project_PT\ProjectSettings\GvhProjectSettings.xml
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.IO.FileOptions options) [0x00000] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean useAsync) [0x00000] in <1071a2cb0cb3433aae80a793c277a048>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int,bool)
  at System.Xml.XmlWriterSettings.CreateWriter (System.String outputFileName) [0x00051] in <cc6807978d504e8ea81a34b1b2ec871c>:0 
  at System.Xml.XmlWriter.Create (System.String outputFileName, System.Xml.XmlWriterSettings settings) [0x0000a] in <cc6807978d504e8ea81a34b1b2ec871c>:0 
  at Google.ProjectSettings.Save () [0x0006d] in /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:821 , Project settings were not saved!
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
Google.Logger:Log (string,Google.LogLevel) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/Logger.cs:136)
Google.ProjectSettings:Save () (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:844)
Google.ProjectSettings:SetBool (string,bool,Google.SettingsLocation) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:485)
Google.ProjectSettings:SetBool (string,bool) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs:497)
Google.IOSResolver:set_VerboseLoggingEnabled (bool) (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/IOSResolver/src/IOSResolver.cs:998)
Google.IOSResolver:.cctor () (at /Users/<USER>/Workspace/git/unity-jar-resolver/source/IOSResolver/src/IOSResolver.cs:692)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: /Users/<USER>/Workspace/git/unity-jar-resolver/source/VersionHandlerImpl/src/ProjectSettings.cs Line: 844)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.473 seconds
Domain Reload Profiling: 1735ms
	BeginReloadAssembly (91ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (128ms)
		LoadAssemblies (158ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1473ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1240ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (25ms)
			ProcessInitializeOnLoadAttributes (1175ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=6c597e7764bfe5043b5829b8aa95c4b2): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 8.69 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4236 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3941.
Memory consumption went from 0.79 GB to 0.78 GB.
Total: 16.599700 ms (FindLiveObjects: 0.221400 ms CreateObjectMapping: 0.076100 ms MarkObjects: 11.892700 ms  DeleteObjects: 4.408700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.274 seconds
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.568 seconds
Domain Reload Profiling: 1842ms
	BeginReloadAssembly (93ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (18ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (136ms)
		LoadAssemblies (170ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1568ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1317ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (26ms)
			ProcessInitializeOnLoadAttributes (1246ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
OnLevelWasLoaded was found on DOTweenComponent
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Transparent' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text Detail' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Unlit/Text' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'UI/Default Font' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Reflective/Bumped VertexLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Lightmapped/Bumped Specular' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Legacy Shaders/Diffuse Fast' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Asset Pipeline Refresh (id=af2d2d7895dc9a84ea94e0347656b189): Total: 0.000 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Refreshing native plugins compatible for Editor in 8.58 ms, found 14 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4236 Unused Serialized files (Serialized files now loaded: 0)
Unloading 1883 unused Assets / (6.3 MB). Loaded Objects now: 3944.
Memory consumption went from 0.79 GB to 0.78 GB.
Total: 68.987700 ms (FindLiveObjects: 0.257700 ms CreateObjectMapping: 0.085300 ms MarkObjects: 12.779000 ms  DeleteObjects: 55.864400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
